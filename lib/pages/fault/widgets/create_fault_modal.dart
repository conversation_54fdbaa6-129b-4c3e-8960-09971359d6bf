import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:rounds/helpers/ui_helper.dart';
import 'package:rounds/be/FAULT_HEADER.dart';
import 'package:rounds/providers/fault/fault_type_provider.dart';
import 'package:rounds/providers/assets/asset_provider.dart';
import 'package:rounds/providers/assets/floc_provider.dart';
import 'package:rounds/providers/fault/fault_header_provider.dart';
import 'package:rounds/helpers/pa_helper.dart';
import 'package:unvired_sdk/unvired_sdk.dart';
import 'package:flutter/foundation.dart';
import 'package:rounds/utils/utils.dart';

import 'package:intl/intl.dart';
import 'package:animated_custom_dropdown/custom_dropdown.dart';



class CreateFaultModal extends ConsumerStatefulWidget {
  final bool isOpen;
  final VoidCallback onClose;
  final String? initialFunctionalLocation;
  final String? initialAsset;

  const CreateFaultModal({
    Key? key,
    required this.isOpen,
    required this.onClose,
    this.initialFunctionalLocation,
    this.initialAsset,
  }) : super(key: key);

  @override
  ConsumerState<CreateFaultModal> createState() => _CreateFaultModalState();
}

class _CreateFaultModalState extends ConsumerState<CreateFaultModal> {
  final _descriptionController = TextEditingController();
  final _longTextController = TextEditingController();
  final _faultNoticedOnController = TextEditingController();
  final _dueOnController = TextEditingController();
  
  String _selectedPriority = '';
  String _selectedFaultType = '';
  String _selectedFaultMode = '';
  String _selectedFunctionalLocation = '';
  String _selectedAsset = '';


  @override
  void initState() {
    super.initState();
    _initializeForm();
  }

  @override
  void didUpdateWidget(CreateFaultModal oldWidget) {
    super.didUpdateWidget(oldWidget);
    // Reinitialize when modal opens
    if (widget.isOpen && !oldWidget.isOpen) {
      _initializeForm();
    }
  }

  @override
  void dispose() {
    _descriptionController.dispose();
    _longTextController.dispose();
    _faultNoticedOnController.dispose();
    _dueOnController.dispose();
    super.dispose();
  }

  void _initializeForm() {
    // Reset form when modal opens
    if (widget.isOpen) {
      _descriptionController.clear();
      _longTextController.clear();
      _faultNoticedOnController.text = DateFormat('dd MMM yyyy').format(DateTime.now());
      _dueOnController.clear();
      _selectedPriority = '';
      _selectedFaultType = '';
      _selectedFaultMode = '';
      _selectedFunctionalLocation = widget.initialFunctionalLocation ?? '';
      _selectedAsset = widget.initialAsset ?? '';

      // Initialize providers with plant data
      _initializeProviders();
    }
  }

  void _initializeProviders() async {
    final plant = ref.read(plantProvider);

    if (plant.isNotEmpty) {
      // Initialize functional location provider
      await ref.read(flocHeaderProvider.notifier).getLocHeaderList(plant);

      // Initialize asset provider
      await ref.read(assetHeaderProvider.notifier).getAssetHeaderList(plant);

      // Initialize fault type list provider
      await ref.read(faultTypeListProvider.notifier).fetchFaultTypeList();

      // Initialize fault mode list provider
      await ref.read(faultModeHeaderListProvider.notifier).fetchFaultModeList();
    }
  }

  bool get _isSaveDisabled {
    return _descriptionController.text.trim().isEmpty ||
           ((_selectedFunctionalLocation.trim().isEmpty || _selectedFunctionalLocation == '--Select--') &&
            (_selectedAsset.trim().isEmpty || _selectedAsset == '--Select--')) ||
           _selectedFaultType.isEmpty ||
           _selectedPriority.isEmpty;
  }

  void _handleSave() async {
    if (_isSaveDisabled || !mounted) return;

    final currentContext = context;

    try {
      // Show loading dialog
      UIHelper().progressDialog(
        context: currentContext,
        message: 'Creating fault...',
      );

      // Create fault header
      final faultHeader = FAULT_HEADER(
        fault_id: UIHelper.generateRandomId(),
        description: _descriptionController.text.trim(),
        details: _longTextController.text.trim(),
        priority: _getPriorityCode(_selectedPriority),
        fault_type: _selectedFaultType.isEmpty ? null : _selectedFaultType,
        failure_mode: _selectedFaultMode.isEmpty ? null : _selectedFaultMode,
        location_id: (_selectedFunctionalLocation.isNotEmpty && _selectedFunctionalLocation != '--Select--')
            ? _extractLocationId(_selectedFunctionalLocation) : null,
        asset_no: (_selectedAsset.isNotEmpty && _selectedAsset != '--Select--')
            ? _extractAssetNo(_selectedAsset) : null,
        status: 'Open',
        reported_by: Utils.getUserId()., // TODO: Get from user provider
        reported_on: _parseDateToInt(_faultNoticedOnController.text),
        req_end: _parseDateToInt(_dueOnController.text),
        p_mode: 'A', // Add mode
      );

      // Save to local database
      await AppDatabaseManager().insert(
        DBInputEntity(FAULT_HEADER.TABLE_NAME, faultHeader.toJson())
      );

      // Send to server
      Result? result;
      if (!kIsWeb) {
        result = await PAHelper.addOrModifyFaultInAsyncMode(currentContext, faultHeader);
      } else {
        result = await PAHelper.addOrModifyFaultInSyncMode(currentContext, faultHeader);
      }

      if (mounted) {
        // Close loading dialog
        Navigator.pop(currentContext);

        // Refresh fault list
        final plant = ref.read(plantProvider);
        ref.read(faultHeaderListProvider.notifier).fetchFaultHeaderList(plant);

        // Show success message
        UIHelper.showResultInfoDialog(
          currentContext,
          description: 'Fault created successfully.',
          onPressed: () {
            Navigator.pop(currentContext);
            widget.onClose();
          },
        );
      }

    } catch (e) {
      if (mounted) {
        // Close loading dialog
        Navigator.pop(currentContext);

        // Show error dialog
        UIHelper.showErrorDialog(
          currentContext,
          description: 'Failed to create fault: ${e.toString()}',
        );
      }
    }
  }

  String _getPriorityCode(String priority) {
    switch (priority) {
      case 'High':
        return '1';
      case 'Medium':
        return '2';
      case 'Low':
        return '3';
      default:
        return '2'; // Default to Medium
    }
  }

  int? _parseDateToInt(String dateStr) {
    if (dateStr.isEmpty) return null;
    try {
      // Try parsing the new format first (dd MMM yyyy)
      DateTime date;
      try {
        date = DateFormat('dd MMM yyyy').parse(dateStr);
      } catch (e) {
        // Fallback to old format (yyyy-MM-dd) for compatibility
        date = DateFormat('yyyy-MM-dd').parse(dateStr);
      }
      return int.parse(DateFormat('yyyyMMdd').format(date));
    } catch (e) {
      return null;
    }
  }

  String? _extractLocationId(String dropdownValue) {
    if (dropdownValue.isEmpty || dropdownValue == '--Select--') return null;
    // Format is "LOCATION_ID - DESCRIPTION"
    final parts = dropdownValue.split(' - ');
    return parts.isNotEmpty ? parts[0] : null;
  }

  int? _extractAssetNo(String dropdownValue) {
    if (dropdownValue.isEmpty || dropdownValue == '--Select--') return null;
    // Format is "ASSET_NO - DESCRIPTION"
    final parts = dropdownValue.split(' - ');
    return parts.isNotEmpty ? int.tryParse(parts[0]) : null;
  }

  @override
  Widget build(BuildContext context) {
    if (!widget.isOpen) return const SizedBox.shrink();

    return Material(
      color: Colors.black.withOpacity(0.5), // bg-gray-800 bg-opacity-75
      child: Center(
        child: Container(
          width: double.infinity,
          constraints: const BoxConstraints(maxWidth: 672), // max-w-2xl (42rem = 672px)
          margin: const EdgeInsets.all(16),
          child: Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(8),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.25),
                  blurRadius: 25,
                  offset: const Offset(0, 10),
                ),
              ],
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                _buildHeader(),
                _buildContent(),
                _buildFooter(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: const BoxDecoration(
        border: Border(
          bottom: BorderSide(color: Color(0xFFE5E7EB)), // border-gray-200
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.warning_amber_outlined,
            size: 24,
            color: const Color(0xFFF97316), // text-orange-500
          ),
          const SizedBox(width: 8),
          Text(
            'Create Fault',
            style: UIHelper.modernTitleStyle(
              fontSize: 20, // text-xl
              fontWeight: FontWeight.w600, // font-semibold
              color: const Color(0xFF1F2937), // text-gray-800
            ),
          ),
          const Spacer(),
          GestureDetector(
            onTap: widget.onClose,
            child: Icon(
              Icons.close,
              size: 24,
              color: const Color(0xFF9CA3AF), // text-gray-400
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildContent() {
    return Flexible(
      child: Container(
        constraints: BoxConstraints(
          maxHeight: MediaQuery.of(context).size.height * 0.65, // max-h-[65vh] exact
        ),
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(24),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildShortDescriptionField(),
              const SizedBox(height: 20),
              _buildLocationAssetRow(),
              const SizedBox(height: 20),
              _buildPriorityField(),
              const SizedBox(height: 20),
              _buildFaultTypeRow(),
              const SizedBox(height: 20),
              _buildDateRow(),
              const SizedBox(height: 20),
              _buildLongTextField(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFooter() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: const BoxDecoration(
        color: Color(0xFFF9FAFB), // bg-gray-50
        border: Border(
          top: BorderSide(color: Color(0xFFE5E7EB)), // border-gray-200
        ),
        borderRadius: BorderRadius.only(
          bottomLeft: Radius.circular(8),
          bottomRight: Radius.circular(8),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          // Cancel button
          GestureDetector(
            onTap: widget.onClose,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              decoration: BoxDecoration(
                color: const Color(0xFFF3F4F6), // bg-gray-100
                borderRadius: BorderRadius.circular(6),
                border: Border.all(color: const Color(0xFFD1D5DB)), // border-gray-300
              ),
              child: Text(
                'Cancel',
                style: UIHelper.modernBodyStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: const Color(0xFF374151), // text-gray-700
                ),
              ),
            ),
          ),
          const SizedBox(width: 12),
          // Save button
          GestureDetector(
            onTap: _isSaveDisabled ? null : _handleSave,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              decoration: BoxDecoration(
                color: _isSaveDisabled 
                    ? const Color(0xFF9CA3AF) // bg-gray-400
                    : const Color(0xFF2563EB), // bg-brand-blue
                borderRadius: BorderRadius.circular(6),
              ),
              child: Text(
                'Save Fault',
                style: UIHelper.modernBodyStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: Colors.white,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFormField(String label, Widget child, {bool isRequired = false}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          isRequired ? '$label*' : label,
          style: UIHelper.modernCaptionStyle(
            fontSize: 12, // text-xs
            fontWeight: FontWeight.w500, // font-medium
            color: const Color(0xFF374151), // text-gray-700
          ),
        ),
        const SizedBox(height: 4),
        child,
      ],
    );
  }

  Widget _buildShortDescriptionField() {
    return _buildFormField(
      'Short Description',
      TextField(
        controller: _descriptionController,
        maxLength: 150,
        decoration: InputDecoration(
          hintText: 'e.g., Pump is leaking oil',
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(6),
            borderSide: const BorderSide(color: Color(0xFFD1D5DB)), // border-gray-300
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(6),
            borderSide: const BorderSide(color: Color(0xFF2563EB)), // focus:border-brand-blue
          ),
          contentPadding: const EdgeInsets.symmetric(horizontal: 10, vertical: 8), // py-2 px-2.5
          counterText: '', // Hide default counter
        ),
        style: UIHelper.modernBodyStyle(
          fontSize: 14, // text-sm
          fontWeight: FontWeight.w400,
          color: const Color(0xFF1F2937), // text-gray-900
        ),
        onChanged: (value) {
          setState(() {}); // Update save button state
        },
      ),
      isRequired: true,
    );
  }

  Widget _buildLocationAssetRow() {
    return Row(
      children: [
        Expanded(
          child: _buildFormField(
            'Functional Location',
            _buildFunctionalLocationDropdown(),
            isRequired: true,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: _buildFormField(
            'Asset',
            _buildAssetDropdown(),
          ),
        ),
      ],
    );
  }

  Widget _buildFunctionalLocationDropdown() {
    final flocList = ref.watch(flocHeaderProvider);

    final dropdownItems = [
      '--Select--',
      ...flocList.map((floc) => '${floc.location_id!} - ${floc.description}')
    ];

    return CustomDropdown<String>.search(
      hintText: 'Search or select location...',
      items: dropdownItems,
      initialItem: (_selectedFunctionalLocation.isNotEmpty && _selectedFunctionalLocation != '--Select--')
          ? _selectedFunctionalLocation
          : null,
      excludeSelected: false,
      decoration: CustomDropdownDecoration(
        closedBorder: Border.all(color: const Color(0xFFD1D5DB)),
        closedBorderRadius: BorderRadius.circular(6),
        expandedBorder: Border.all(color: const Color(0xFF2563EB)),
        expandedBorderRadius: BorderRadius.circular(6),
        hintStyle: UIHelper.modernBodyStyle(
          fontSize: 14,
          color: const Color(0xFF9CA3AF),
        ),
        listItemStyle: UIHelper.modernBodyStyle(
          fontSize: 14,
          color: const Color(0xFF1F2937),
        ),
        headerStyle: UIHelper.modernBodyStyle(
          fontSize: 14,
          color: const Color(0xFF1F2937),
        ),
      ),
      onChanged: (newValue) {
        if (newValue != null) {
          setState(() {
            if (newValue == '--Select--') {
              _selectedFunctionalLocation = '';
              _selectedAsset = ''; // Clear asset when location is cleared
            } else {
              _selectedFunctionalLocation = newValue;
              _selectedAsset = ''; // Clear asset when location is selected
            }
          });
        }
      },
    );
  }

  Widget _buildAssetDropdown() {
    final assetList = ref.watch(assetHeaderProvider);
    final bool isAssetDisabled = _selectedFunctionalLocation.isNotEmpty && _selectedFunctionalLocation != '--Select--';

    if (isAssetDisabled) {
      // Show disabled state
      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 12),
        decoration: BoxDecoration(
          color: const Color(0xFFF9FAFB),
          border: Border.all(color: const Color(0xFFE5E7EB)),
          borderRadius: BorderRadius.circular(6),
        ),
        child: Text(
          'Asset disabled (location selected)',
          style: UIHelper.modernBodyStyle(
            fontSize: 14,
            color: const Color(0xFF9CA3AF),
          ),
        ),
      );
    }

    final dropdownItems = [
      '--Select--',
      ...assetList.map((asset) => '${asset.asset_no.toString()} - ${asset.description}')
    ];

    return CustomDropdown<String>.search(
      hintText: 'Search or select asset...',
      items: dropdownItems,
      initialItem: (_selectedAsset.isNotEmpty && _selectedAsset != '--Select--')
          ? _selectedAsset
          : null,
      excludeSelected: false,
      decoration: CustomDropdownDecoration(
        closedBorder: Border.all(color: const Color(0xFFD1D5DB)),
        closedBorderRadius: BorderRadius.circular(6),
        expandedBorder: Border.all(color: const Color(0xFF2563EB)),
        expandedBorderRadius: BorderRadius.circular(6),
        hintStyle: UIHelper.modernBodyStyle(
          fontSize: 14,
          color: const Color(0xFF9CA3AF),
        ),
        listItemStyle: UIHelper.modernBodyStyle(
          fontSize: 14,
          color: const Color(0xFF1F2937),
        ),
        headerStyle: UIHelper.modernBodyStyle(
          fontSize: 14,
          color: const Color(0xFF1F2937),
        ),
      ),
      onChanged: (newValue) {
        if (newValue != null) {
          setState(() {
            if (newValue == '--Select--') {
              _selectedAsset = '';
            } else {
              _selectedAsset = newValue;
              _selectedFunctionalLocation = ''; // Clear location when asset is selected
            }
          });
        }
      },
    );
  }



  Widget _buildPriorityField() {
    return _buildFormField(
      'Priority',
      Row(
        children: [
          _buildPriorityButton('High', '1'),
          const SizedBox(width: 8),
          _buildPriorityButton('Medium', '2'),
          const SizedBox(width: 8),
          _buildPriorityButton('Low', '3'),
        ],
      ),
      isRequired: true,
    );
  }

  Widget _buildPriorityButton(String label, String value) {
    final isSelected = _selectedPriority == label;
    Color backgroundColor;
    Color textColor;

    if (isSelected) {
      switch (label) {
        case 'High':
          backgroundColor = const Color(0xFFDC2626);
          textColor = Colors.white;
          break;
        case 'Medium':
          backgroundColor = const Color(0xFFD97706); // amber-600 to match React Native
          textColor = Colors.white;
          break;
        case 'Low':
          backgroundColor = const Color(0xFF10B981); // emerald-500 (green, not blue)
          textColor = Colors.white;
          break;
        default:
          backgroundColor = const Color(0xFF6B7280);
          textColor = Colors.white;
      }
    } else {
      // Unselected state with priority-specific light backgrounds
      switch (label) {
        case 'High':
          backgroundColor = const Color(0xFFFEE2E2); // red-100
          textColor = const Color(0xFFDC2626); // red-600
          break;
        case 'Medium':
          backgroundColor = const Color(0xFFFEF3C7); // yellow-100
          textColor = const Color(0xFFD97706); // yellow-600
          break;
        case 'Low':
          backgroundColor = const Color(0xFFECFDF5); // emerald-50 (green background)
          textColor = const Color(0xFF10B981); // emerald-500 (green text)
          break;
        default:
          backgroundColor = const Color(0xFFF3F4F6); // bg-gray-100
          textColor = const Color(0xFF6B7280); // text-gray-500
      }
    }

    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedPriority = label;
        });
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 4), // More compact to match React Native
        decoration: BoxDecoration(
          color: backgroundColor,
          borderRadius: BorderRadius.circular(6),
          border: Border.all(
            color: isSelected ? backgroundColor : const Color(0xFFD1D5DB),
            width: 1.0, // Explicit border width
          ),
        ),
        child: Text(
          label,
          style: UIHelper.modernBodyStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: textColor,
          ),
        ),
      ),
    );
  }

  Widget _buildFaultTypeRow() {
    return Row(
      children: [
        Expanded(
          child: _buildFormField(
            'Fault Type',
            _buildFaultTypeDropdown(),
            isRequired: true,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: _buildFormField(
            'Fault Mode',
            _buildFaultModeDropdown(),
          ),
        ),
      ],
    );
  }

  Widget _buildFaultTypeDropdown() {
    final faultTypeList = ref.watch(faultTypeListProvider);

    return DropdownButtonFormField<String>(
      value: _selectedFaultType.isEmpty ? null : _selectedFaultType,
      decoration: InputDecoration(
        hintText: 'Select fault type...',
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(6),
          borderSide: const BorderSide(color: Color(0xFFD1D5DB)),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(6),
          borderSide: const BorderSide(color: Color(0xFF2563EB)),
        ),
        contentPadding: const EdgeInsets.symmetric(horizontal: 10, vertical: 8),
      ),
      items: [
        const DropdownMenuItem<String>(
          value: '',
          child: Text('--Select--'),
        ),
        ...faultTypeList.map((faultType) => DropdownMenuItem<String>(
          value: faultType.fault_code,
          child: Text(faultType.description ?? faultType.fault_code ?? ''),
        )),
      ],
      onChanged: (value) {
        setState(() {
          _selectedFaultType = value ?? '';
        });
      },
    );
  }

  Widget _buildFaultModeDropdown() {
    final faultModeList = ref.watch(faultModeHeaderListProvider);

    return DropdownButtonFormField<String>(
      value: _selectedFaultMode.isEmpty ? null : _selectedFaultMode,
      decoration: InputDecoration(
        hintText: 'Select fault mode...',
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(6),
          borderSide: const BorderSide(color: Color(0xFFD1D5DB)),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(6),
          borderSide: const BorderSide(color: Color(0xFF2563EB)),
        ),
        contentPadding: const EdgeInsets.symmetric(horizontal: 10, vertical: 8),
      ),
      items: [
        const DropdownMenuItem<String>(
          value: '',
          child: Text('--Select--'),
        ),
        ...faultModeList.map((faultMode) => DropdownMenuItem<String>(
          value: faultMode.failure_code,
          child: Text(faultMode.description ?? faultMode.failure_code ?? ''),
        )),
      ],
      onChanged: (value) {
        setState(() {
          _selectedFaultMode = value ?? '';
        });
      },
    );
  }

  Widget _buildDateRow() {
    return Row(
      children: [
        Expanded(
          child: _buildFormField(
            'Fault Noticed On',
            _buildDateField(_faultNoticedOnController),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: _buildFormField(
            'Due Date',
            _buildDateField(_dueOnController),
          ),
        ),
      ],
    );
  }

  Widget _buildDateField(TextEditingController controller) {
    return TextField(
      controller: controller,
      decoration: InputDecoration(
        hintText: 'Select date...',
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(6),
          borderSide: const BorderSide(color: Color(0xFFD1D5DB)),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(6),
          borderSide: const BorderSide(color: Color(0xFF2563EB)),
        ),
        contentPadding: const EdgeInsets.symmetric(horizontal: 10, vertical: 8),
        suffixIcon: const Icon(Icons.calendar_today, size: 16),
      ),
      readOnly: true,
      onTap: () async {
        final date = await showDatePicker(
          context: context,
          initialDate: DateTime.now(),
          firstDate: DateTime(2000),
          lastDate: DateTime(2100),
        );
        if (date != null) {
          controller.text = DateFormat('dd MMM yyyy').format(date);
        }
      },
    );
  }

  Widget _buildLongTextField() {
    return _buildFormField(
      'Long Text / Details',
      TextField(
        controller: _longTextController,
        maxLines: 4,
        decoration: InputDecoration(
          hintText: 'Detailed notes about the fault...',
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(6),
            borderSide: const BorderSide(color: Color(0xFFD1D5DB)),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(6),
            borderSide: const BorderSide(color: Color(0xFF2563EB)),
          ),
          contentPadding: const EdgeInsets.symmetric(horizontal: 10, vertical: 8),
        ),
        style: UIHelper.modernBodyStyle(
          fontSize: 14,
          fontWeight: FontWeight.w400,
          color: const Color(0xFF1F2937),
        ),
      ),
    );
  }




}