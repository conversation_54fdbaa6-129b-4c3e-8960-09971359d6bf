import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:rounds/helpers/ui_helper.dart';
import 'package:rounds/utils/app_colors.dart';
import 'package:rounds/be/FAULT_HEADER.dart';
import 'package:rounds/be/DOCUMENT_HEADER.dart';
import 'package:rounds/be/DOCUMENT_ATTACHMENT.dart';
import 'package:rounds/be/FAULT_DOCUMENT.dart';
import 'package:rounds/providers/fault/fault_header_provider.dart';
import 'package:rounds/providers/fault/fault_type_provider.dart';
import 'package:rounds/providers/assets/asset_provider.dart';
import 'package:rounds/providers/assets/floc_provider.dart';
import 'package:rounds/helpers/pa_helper.dart';
import 'package:rounds/helpers/db_helper.dart';
import 'package:rounds/pages/fault/fault_filter_provider.dart';
import 'package:rounds/pages/fault/fault_screen.dart';

import 'package:rounds/utils/fault_status_helper.dart';
import 'package:rounds/utils/constants.dart';
import 'package:rounds/utils/app_constants.dart';
import 'package:rounds/be/FAULT_ACTION.dart';
import 'package:rounds/be/USER_HEADER.dart';
import 'package:rounds/providers/job_creation/job_header_provider.dart';
import 'package:rounds/providers/user_provider.dart';
import 'package:rounds/pages/fault/widgets/modern_job_creation_modal.dart';


import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:unvired_sdk/unvired_sdk.dart';
import 'package:flutter/foundation.dart';
import 'package:intl/intl.dart';
import 'dart:convert';
import 'dart:io';

class ModernFaultDetailView extends ConsumerStatefulWidget {
  final String type;
  final bool isBackRequired;

  const ModernFaultDetailView({
    Key? key,
    required this.type,
    this.isBackRequired = true,
  }) : super(key: key);

  @override
  ConsumerState<ModernFaultDetailView> createState() => _ModernFaultDetailViewState();
}

class _ModernFaultDetailViewState extends ConsumerState<ModernFaultDetailView> {
  bool _isEditing = false;
  FAULT_HEADER? _editedFaultHeader;
  String _activeTab = 'details'; // 'details' or 'attachments'

  final _descriptionController = TextEditingController();
  final _longTextController = TextEditingController();
  final _faultNoticedOnController = TextEditingController();
  final _dueOnController = TextEditingController();
  String _selectedPriority = 'Medium';
  String _selectedFaultType = '';
  String _selectedFaultMode = '';

  // Real attachments data using proper backend entities
  List<DOCUMENT_HEADER> _attachments = [];

  @override
  void dispose() {
    _descriptionController.dispose();
    _longTextController.dispose();
    _faultNoticedOnController.dispose();
    _dueOnController.dispose();
    super.dispose();
  }

  String _getPriorityText(String? priority) {
    if (priority == null) return 'Medium';
    switch (priority) {
      case '1':
        return 'High';
      case '2':
        return 'Medium';
      case '3':
        return 'Low';
      default:
        return 'Medium';
    }
  }

  void _toggleEditMode() {
    final faultHeader = ref.read(faultHeaderProvider);

    // Only allow edit mode for Open faults
    if (!FaultStatusHelper.shouldShowEditButton(faultHeader)) {
      UIHelper.showResultInfoDialog(
        context,
        description: FaultStatusHelper.getReadOnlyReason(faultHeader),
        onPressed: () => Navigator.pop(context),
      );
      return;
    }

    setState(() {
      _isEditing = !_isEditing;
      if (!_isEditing) {
        // Reset edit data when canceling
        _editedFaultHeader = null;
        _descriptionController.clear();
        _longTextController.clear();
        _faultNoticedOnController.clear();
        _dueOnController.clear();
      }
    });
  }

  void _saveChanges() async {
    if (_editedFaultHeader != null && mounted) {
      try {
        // Show loading dialog
        UIHelper().progressDialog(
          context: context,
          message: 'Saving changes...',
        );

        // Update the edited fault header with form data
        _editedFaultHeader!.description = _descriptionController.text;
        _editedFaultHeader!.details = _longTextController.text;

        // Update priority (convert text to priority code)
        switch (_selectedPriority) {
          case 'High':
            _editedFaultHeader!.priority = '1';
            break;
          case 'Medium':
            _editedFaultHeader!.priority = '2';
            break;
          case 'Low':
            _editedFaultHeader!.priority = '3';
            break;
        }

        // Update fault type and mode
        _editedFaultHeader!.fault_type = _selectedFaultType.isEmpty ? null : _selectedFaultType;
        _editedFaultHeader!.failure_mode = _selectedFaultMode.isEmpty ? null : _selectedFaultMode;

        // Parse and update dates
        if (_faultNoticedOnController.text.isNotEmpty) {
          try {
            // Try parsing the new format first (dd MMM yyyy)
            DateTime date;
            try {
              date = DateFormat('dd MMM yyyy').parse(_faultNoticedOnController.text);
            } catch (e) {
              // Fallback to old format (yyyy-MM-dd) for compatibility
              date = DateFormat('yyyy-MM-dd').parse(_faultNoticedOnController.text);
            }
            final dateAsInt = int.parse(DateFormat('yyyyMMdd').format(date));
            _editedFaultHeader!.reported_on = dateAsInt;
          } catch (e) {
            // Keep original date if parsing fails
          }
        }

        if (_dueOnController.text.isNotEmpty) {
          try {
            // Try parsing the new format first (dd MMM yyyy)
            DateTime date;
            try {
              date = DateFormat('dd MMM yyyy').parse(_dueOnController.text);
            } catch (e) {
              // Fallback to old format (yyyy-MM-dd) for compatibility
              date = DateFormat('yyyy-MM-dd').parse(_dueOnController.text);
            }
            final dateAsInt = int.parse(DateFormat('yyyyMMdd').format(date));
            _editedFaultHeader!.req_end = dateAsInt;
          } catch (e) {
            // Keep original date if parsing fails
          }
        }

        // Save to local database
        await AppDatabaseManager().update(
          DBInputEntity(FAULT_HEADER.TABLE_NAME, _editedFaultHeader!.toJson())
        );

        // Send to server
        if (mounted) {
          if (!kIsWeb) {
            await PAHelper.addOrModifyFaultInAsyncMode(context, _editedFaultHeader!);
          } else {
            await PAHelper.addOrModifyFaultInSyncMode(context, _editedFaultHeader!);
          }
        }

        // Handle attachments - upload new ones to server
        final newAttachments = _attachments.where((att) => att.objectStatus == ObjectStatus.add).toList();
        if (newAttachments.isNotEmpty) {
          if (kIsWeb) {
            for (DOCUMENT_HEADER document in newAttachments) {
              var doc = await DbHelper().getAttachmentFromIndexDbByUid(document.doc_id ?? "");
              await SyncEngine().uploadAttachmentSync(
                  doc ?? "", document.file_name ?? "", document.doc_id ?? "");
            }
            if (mounted) {
              await PAHelper.addDocumentInSyncMode(context, newAttachments);
            }
          } else {
            if (mounted) {
              await PAHelper.addDocumentInAsyncMode(context, newAttachments);
            }
          }
        }

        // Update the provider with the new data
        ref.read(faultHeaderProvider.notifier).resetFault(_editedFaultHeader!);

        if (mounted) {
          // Close loading dialog
          Navigator.pop(context);

          // Show success message
          UIHelper.showResultInfoDialog(
            context,
            description: 'Fault updated successfully.',
            onPressed: () {
              Navigator.pop(context); // Close success dialog only
            },
          );

          setState(() {
            _isEditing = false;
            _editedFaultHeader = null;
          });
        }

      } catch (e) {
        if (mounted) {
          // Close loading dialog
          Navigator.pop(context);

          // Show error dialog
          UIHelper.showErrorDialog(
            context,
            description: 'Failed to save changes: ${e.toString()}',
          );
        }
      }
    }
  }

  Widget _buildFormField(String label, Widget child) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: UIHelper.modernCaptionStyle(
            fontSize: 12, // Matching React Native text-xs
            fontWeight: FontWeight.w500,
            color: Colors.grey.shade700,
          ),
        ),
        const SizedBox(height: 4),
        child,
      ],
    );
  }

  Widget _buildPriorityButtonGroup() {
    return Row(
      children: [
        _buildPriorityButton('High', '1'),
        const SizedBox(width: 8),
        _buildPriorityButton('Medium', '2'),
        const SizedBox(width: 8),
        _buildPriorityButton('Low', '3'),
      ],
    );
  }

  Widget _buildPriorityButton(String label, String value) {
    final isSelected = _selectedPriority == label;
    Color backgroundColor;
    Color textColor;

    if (isSelected) {
      switch (label) {
        case 'High':
          backgroundColor = const Color(0xFFDC2626);
          textColor = Colors.white;
          break;
        case 'Medium':
          backgroundColor = const Color(0xFFD97706); // amber-600 to match React Native
          textColor = Colors.white;
          break;
        case 'Low':
          backgroundColor = const Color(0xFF10B981); // emerald-500 (green, not blue)
          textColor = Colors.white;
          break;
        default:
          backgroundColor = const Color(0xFF6B7280);
          textColor = Colors.white;
      }
    } else {
      // Unselected state with priority-specific light backgrounds
      switch (label) {
        case 'High':
          backgroundColor = const Color(0xFFFEE2E2); // red-100
          textColor = const Color(0xFFDC2626); // red-600
          break;
        case 'Medium':
          backgroundColor = const Color(0xFFFEF3C7); // yellow-100
          textColor = const Color(0xFFD97706); // yellow-600
          break;
        case 'Low':
          backgroundColor = const Color(0xFFECFDF5); // emerald-50 (green background)
          textColor = const Color(0xFF10B981); // emerald-500 (green text)
          break;
        default:
          backgroundColor = Colors.grey.shade100;
          textColor = Colors.grey.shade600;
      }
    }

    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedPriority = label;
        });
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 4), // More compact to match React Native
        decoration: BoxDecoration(
          color: backgroundColor,
          borderRadius: BorderRadius.circular(6),
          border: Border.all(
            color: isSelected ? backgroundColor : const Color(0xFFD1D5DB), // Consistent border color
            width: 1.0, // Explicit border width
          ),
        ),
        child: Text(
          label,
          style: UIHelper.modernBodyStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: textColor,
          ),
        ),
      ),
    );
  }

  Widget _buildFaultTypeDropdown(WidgetRef ref) {
    // Get fault type options from provider
    final faultTypeList = ref.watch(faultTypeListProvider);

    return DropdownButtonFormField<String>(
      value: _selectedFaultType.isEmpty ? null : _selectedFaultType,
      decoration: InputDecoration(
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(6),
          borderSide: BorderSide(color: Colors.grey.shade300),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(6),
          borderSide: const BorderSide(color: Color(0xFF2563EB)),
        ),
        contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      ),
      items: [
        const DropdownMenuItem<String>(
          value: '',
          child: Text('--Select--'),
        ),
        ...faultTypeList.map((faultType) => DropdownMenuItem<String>(
          value: faultType.fault_code,
          child: Text(faultType.description ?? faultType.fault_code ?? ''),
        )),
      ],
      onChanged: (value) {
        setState(() {
          _selectedFaultType = value ?? '';
        });
      },
    );
  }

  Widget _buildFaultModeDropdown(WidgetRef ref) {
    // Get fault mode options from provider
    final faultModeList = ref.watch(faultModeHeaderListProvider);

    return DropdownButtonFormField<String>(
      value: _selectedFaultMode.isEmpty ? null : _selectedFaultMode,
      decoration: InputDecoration(
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(6),
          borderSide: BorderSide(color: Colors.grey.shade300),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(6),
          borderSide: const BorderSide(color: Color(0xFF2563EB)),
        ),
        contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      ),
      items: [
        const DropdownMenuItem<String>(
          value: '',
          child: Text('--Select--'),
        ),
        ...faultModeList.map((faultMode) => DropdownMenuItem<String>(
          value: faultMode.failure_code,
          child: Text(faultMode.description ?? faultMode.failure_code ?? ''),
        )),
      ],
      onChanged: (value) {
        setState(() {
          _selectedFaultMode = value ?? '';
        });
      },
    );
  }

  Widget _buildDateField(TextEditingController controller) {
    final faultHeader = ref.read(faultHeaderProvider);
    final isEditable = FaultStatusHelper.shouldShowEditButton(faultHeader);

    return TextField(
      controller: controller,
      enabled: isEditable, // Disable for read-only faults
      decoration: InputDecoration(
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(6),
          borderSide: BorderSide(color: Colors.grey.shade300),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(6),
          borderSide: const BorderSide(color: Color(0xFF2563EB)),
        ),
        disabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(6),
          borderSide: BorderSide(color: Colors.grey.shade200),
        ),
        fillColor: isEditable ? Colors.white : Colors.grey.shade50,
        filled: true,
        contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        suffixIcon: Icon(
          Icons.calendar_today,
          size: 16,
          color: isEditable ? null : Colors.grey.shade400,
        ),
      ),
      style: UIHelper.modernBodyStyle(
        fontSize: 14,
        fontWeight: FontWeight.w400,
        color: isEditable ? AppColors.modernPrimaryText : Colors.grey.shade600,
      ),
      readOnly: true,
      onTap: isEditable ? () async {
        // Parse current date if available
        DateTime initialDate = DateTime.now();
        if (controller.text.isNotEmpty) {
          try {
            initialDate = DateFormat('dd MMM yyyy').parse(controller.text);
          } catch (e) {
            // Use current date if parsing fails
          }
        }

        final date = await showDatePicker(
          context: context,
          initialDate: initialDate,
          firstDate: DateTime(2000),
          lastDate: DateTime(2100),
        );
        if (date != null) {
          controller.text = DateFormat('dd MMM yyyy').format(date);
        }
      } : null,
    );
  }

  @override
  Widget build(BuildContext context) {
    final faultHeader = ref.watch(faultHeaderProvider);

    // Initialize edit data when entering edit mode
    if (_isEditing && _editedFaultHeader == null) {
      _editedFaultHeader = FAULT_HEADER.fromJson(faultHeader.toJson());
      _descriptionController.text = _editedFaultHeader?.description ?? '';
      _longTextController.text = _editedFaultHeader?.details ?? '';
      // Format dates properly for display
      if (_editedFaultHeader?.reported_on != null) {
        _faultNoticedOnController.text = UIHelper.formatDate(_editedFaultHeader!.reported_on.toString());
      }
      if (_editedFaultHeader?.req_end != null) {
        _dueOnController.text = UIHelper.formatDate(_editedFaultHeader!.req_end.toString());
      }
      _selectedPriority = _getPriorityText(_editedFaultHeader?.priority);
      _selectedFaultType = _editedFaultHeader?.fault_type ?? '';
      _selectedFaultMode = _editedFaultHeader?.failure_mode ?? '';

      // Initialize attachments (mock data for now - replace with actual data)
      _initializeAttachments();
    }
    
    return Container(
      color: const Color(0xFFF1F3F4), // Darker grey than list background
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // First Card: Header with fault number, description, badges
            _buildHeaderCard(context, ref, faultHeader),
            const SizedBox(height: 16),

            // Tabs
            _buildTabs(context),
            const SizedBox(height: 16),

            // Second Card: Content sections
            Expanded(
              child: _buildContentCard(context, ref, faultHeader),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHeaderCard(BuildContext context, WidgetRef ref, FAULT_HEADER faultHeader) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header row with fault number and edit button
          Row(
            children: [
              Row(
                children: [
                  Text(
                    '${faultHeader.fault_id ?? 'NEW'} Fault Details',
                    style: UIHelper.modernTitleStyle(
                      fontSize: 20, // Matching React Native text-xl (20px)
                      fontWeight: FontWeight.w600,
                      color: AppColors.modernPrimaryText,
                    ),
                  ),
                  if (FaultStatusHelper.isFaultReadOnly(faultHeader)) ...[
                    const SizedBox(width: 12),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: Colors.grey.shade100,
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(color: Colors.grey.shade300),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            Icons.lock_outline,
                            size: 14,
                            color: Colors.grey.shade600,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            'Read Only',
                            style: UIHelper.modernBodyStyle(
                              fontSize: 12,
                              fontWeight: FontWeight.w500,
                              color: Colors.grey.shade600,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ],
              ),
              const Spacer(),
              if (_isEditing) ...[
                // Cancel button
                GestureDetector(
                  onTap: _toggleEditMode,
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(6),
                      border: Border.all(color: Colors.grey.shade300),
                    ),
                    child: Text(
                      'Cancel',
                      style: UIHelper.modernBodyStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                        color: Colors.grey.shade700,
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                // Save button
                GestureDetector(
                  onTap: _saveChanges,
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    decoration: BoxDecoration(
                      color: const Color(0xFF059669), // Green color like React Native
                      borderRadius: BorderRadius.circular(6),
                    ),
                    child: Text(
                      'Save Changes',
                      style: UIHelper.modernBodyStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ),
              ] else ...[
                // Show action buttons only for Open faults
                if (FaultStatusHelper.shouldShowActionButtons(faultHeader)) ...[
                  // Create Job button
                  if (FaultStatusHelper.shouldShowCreateJobButton(faultHeader))
                    _buildActionButton(
                      'Create Job',
                      Icons.assignment_turned_in,
                      const Color(0xFF2563EB),
                      () => _handleCreateJob(context, ref, faultHeader),
                    ),
                  if (FaultStatusHelper.shouldShowCreateJobButton(faultHeader))
                    const SizedBox(width: 8),

                  // Complete button
                  if (FaultStatusHelper.shouldShowCompleteButton(faultHeader))
                    _buildActionButton(
                      'Complete',
                      Icons.flag,
                      const Color(0xFF059669),
                      () => _handleComplete(context, ref, faultHeader),
                    ),
                  if (FaultStatusHelper.shouldShowCompleteButton(faultHeader))
                    const SizedBox(width: 8),

                  // Edit button - conditional based on fault status
                  if (FaultStatusHelper.shouldShowEditButton(faultHeader))
                    GestureDetector(
                      onTap: _toggleEditMode,
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                        decoration: BoxDecoration(
                          color: const Color(0xFF2563EB), // Brand blue like React Native
                          borderRadius: BorderRadius.circular(6),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            const Icon(
                              Icons.edit,
                              size: 16,
                              color: Colors.white,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              'Edit',
                              style: UIHelper.modernBodyStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.w500,
                                color: Colors.white,
                              ),
                            ),
                          ],
                        ),
                      ),
                    )
                  else
                    // Disabled edit button for non-Open faults
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                      decoration: BoxDecoration(
                        color: Colors.grey.shade200,
                        borderRadius: BorderRadius.circular(6),
                        border: Border.all(color: Colors.grey.shade300),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            Icons.edit_off,
                            size: 16,
                            color: Colors.grey.shade500,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            'Edit Disabled',
                            style: UIHelper.modernBodyStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.w500,
                              color: Colors.grey.shade500,
                            ),
                          ),
                        ],
                      ),
                    ),
                ] else
                  // Show read-only indicator for non-Open faults
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: Colors.grey.shade100,
                      borderRadius: BorderRadius.circular(6),
                      border: Border.all(color: Colors.grey.shade300),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          Icons.lock_outline,
                          size: 16,
                          color: Colors.grey.shade600,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          'Read Only',
                          style: UIHelper.modernBodyStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                            color: Colors.grey.shade600,
                          ),
                        ),
                      ],
                    ),
                  ),
              ],
            ],
          ),
          const SizedBox(height: 16),

          // Read-only status indicator for non-Open faults
          if (FaultStatusHelper.isFaultReadOnly(faultHeader))
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              margin: const EdgeInsets.only(bottom: 16),
              decoration: BoxDecoration(
                color: Colors.amber.shade50,
                borderRadius: BorderRadius.circular(6),
                border: Border.all(color: Colors.amber.shade200),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.info_outline,
                    size: 20,
                    color: Colors.amber.shade700,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      FaultStatusHelper.getReadOnlyReason(faultHeader),
                      style: UIHelper.modernBodyStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                        color: Colors.amber.shade700,
                      ),
                    ),
                  ),
                ],
              ),
            ),

          // Description - Edit mode or display mode
          if (_isEditing) ...[
            // Edit mode with FormField style
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Short Description',
                      style: UIHelper.modernCaptionStyle(
                        fontSize: 12, // Matching React Native text-xs
                        fontWeight: FontWeight.w500,
                        color: Colors.grey.shade700,
                      ),
                    ),
                    Text(
                      '${_descriptionController.text.length}/150',
                      style: UIHelper.modernCaptionStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.w400,
                        color: Colors.grey.shade500,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 4),
                TextField(
                  controller: _descriptionController,
                  maxLength: 150,
                  enabled: FaultStatusHelper.shouldShowEditButton(faultHeader), // Disable for read-only faults
                  decoration: InputDecoration(
                    hintText: 'e.g., Pump is leaking oil',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(6),
                      borderSide: BorderSide(color: Colors.grey.shade300),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(6),
                      borderSide: const BorderSide(color: Color(0xFF2563EB)),
                    ),
                    disabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(6),
                      borderSide: BorderSide(color: Colors.grey.shade200),
                    ),
                    fillColor: FaultStatusHelper.shouldShowEditButton(faultHeader)
                        ? Colors.white
                        : Colors.grey.shade50,
                    filled: true,
                    contentPadding: const EdgeInsets.symmetric(horizontal: 10, vertical: 8), // Matching React Native py-2 px-2.5
                    counterText: '', // Hide default counter
                  ),
                  style: UIHelper.modernBodyStyle(
                    fontSize: 14, // Matching React Native text-sm
                    fontWeight: FontWeight.w400,
                    color: FaultStatusHelper.shouldShowEditButton(faultHeader)
                        ? AppColors.modernPrimaryText
                        : Colors.grey.shade600,
                  ),
                  onChanged: (value) {
                    setState(() {}); // Update character counter
                  },
                ),
              ],
            ),
          ] else
            Text(
              faultHeader.description ?? 'No description available',
              style: UIHelper.modernBodyStyle(
                fontSize: 16, // Matching React Native text-md (16px)
                fontWeight: FontWeight.w500, // Matching React Native font-medium
                color: AppColors.modernPrimaryText,
              ),
            ),
          const SizedBox(height: 16),

          // Status badges with lighter colors
          _buildLightStatusBadges(context, ref, faultHeader),
        ],
      ),
    );
  }

  Widget _buildLightStatusBadges(BuildContext context, WidgetRef ref, FAULT_HEADER faultHeader) {
    return Row(
      children: [
        // Priority badge with light colors
        _buildLightPriorityBadge(ref, faultHeader),
        const SizedBox(width: 8),

        // Status badge with light colors
        _buildLightStatusBadge(faultHeader),
        const SizedBox(width: 8),

        // Job badge if assigned with light colors (combined badge)
        if (faultHeader.job_id != null)
          _buildLightJobBadge(faultHeader),
      ],
    );
  }

  Widget _buildLightJobBadge(FAULT_HEADER faultHeader) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        // "Job Assigned" part with lighter greenish color
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          decoration: BoxDecoration(
            color: const Color(0xFF10B981).withOpacity(0.1),
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(16),
              bottomLeft: Radius.circular(16),
            ),
            border: Border.all(color: const Color(0xFF10B981).withOpacity(0.3)),
          ),
          child: Text(
            'Job Assigned',
            style: UIHelper.modernCaptionStyle(
              fontSize: 14, // Matching React Native text-sm (14px) for larger badges
              fontWeight: FontWeight.w600, // font-semibold equivalent
              color: const Color(0xFF10B981),
            ),
          ),
        ),
        // Job number part with lighter blue-grey color and spanner icon
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          decoration: BoxDecoration(
            color: const Color(0xFF6B7280).withOpacity(0.1),
            borderRadius: const BorderRadius.only(
              topRight: Radius.circular(16),
              bottomRight: Radius.circular(16),
            ),
            border: Border.all(color: const Color(0xFF6B7280).withOpacity(0.3)),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Icon(
                Icons.build_outlined,
                size: 14,
                color: Color(0xFF6B7280),
              ),
              const SizedBox(width: 4),
              Text(
                faultHeader.job_id.toString(),
                style: UIHelper.modernCaptionStyle(
                  fontSize: 14, // Matching React Native text-sm (14px) for larger badges
                  fontWeight: FontWeight.w600, // font-semibold equivalent
                  color: const Color(0xFF6B7280),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildContentCard(BuildContext context, WidgetRef ref, FAULT_HEADER faultHeader) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: SingleChildScrollView(
        child: _activeTab == 'details'
            ? Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildFaultAttributes(context, ref, faultHeader),
                  const SizedBox(height: 32),
                  _buildContext(context, ref, faultHeader),
                  const SizedBox(height: 32),
                  _buildLongText(context, faultHeader),
                ],
              )
            : _buildAttachmentsContent(context, ref, faultHeader),
      ),
    );
  }

  Widget _buildAttachmentsContent(BuildContext context, WidgetRef ref, FAULT_HEADER faultHeader) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Attachments',
          style: UIHelper.modernBodyStyle(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: const Color(0xFF1F2937), // gray-800
          ),
        ),
        const SizedBox(height: 16),

        // Show content based on state
        if (_attachments.isEmpty && !_isEditing)
          // Empty state when no attachments and not editing
          _buildEmptyAttachmentsState()
        else
          // Grid layout for attachments (matching RN design)
          Wrap(
            spacing: 20, // Increased spacing to match RN
            runSpacing: 30, // More space for filename below thumbnails
            children: [
              // Attachments are always view-only - no add button

              // Existing attachments
              ..._attachments.map((attachment) => _buildAttachmentItem(attachment)),
            ],
          ),
      ],
    );
  }

  // Removed _buildAddAttachmentButton - attachments are view-only

  Widget _buildAttachmentItem(DOCUMENT_HEADER attachment) {
    final fileName = attachment.file_name ?? 'Unknown';

    return Column(
      children: [
        // Attachment thumbnail
        Container(
          width: 120, // Larger size to match RN design
          height: 120,
          decoration: BoxDecoration(
            border: Border.all(color: const Color(0xFFE5E7EB)), // gray-200
            borderRadius: BorderRadius.circular(8),
            color: const Color(0xFFF9FAFB), // gray-50
          ),
          child: Stack(
            children: [
              // Attachment preview
              Positioned.fill(
                child: GestureDetector(
                  onTap: () => _previewAttachment(attachment),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(7),
                    child: _buildAttachmentPreview(attachment),
                  ),
                ),
              ),

              // Delete button (only in edit mode)
              if (_isEditing)
                Positioned(
                  top: -4,
                  right: -4,
                  child: GestureDetector(
                    onTap: () => _removeAttachment(attachment.doc_id ?? ''),
                    child: Container(
                      width: 20,
                      height: 20,
                      decoration: const BoxDecoration(
                        color: Color(0xFFEF4444), // red-500
                        shape: BoxShape.circle,
                      ),
                      child: const Icon(
                        Icons.close,
                        size: 12,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ),
            ],
          ),
        ),
        const SizedBox(height: 8), // Space between thumbnail and filename
        // Filename below thumbnail
        SizedBox(
          width: 120, // Match thumbnail width
          child: Text(
            fileName,
            style: UIHelper.modernBodyStyle(
              fontSize: 12,
              fontWeight: FontWeight.w500,
              color: const Color(0xFF6B7280), // gray-500
            ),
            textAlign: TextAlign.center,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }

  Widget _buildAttachmentPreview(DOCUMENT_HEADER attachment) {
    final fileName = attachment.file_name ?? '';
    final mimeType = attachment.mime_type ?? '';
    final extension = fileName.isNotEmpty ? _getFileExtension(fileName) : '';

    // Determine file type from mime type or extension
    String type = 'document';
    if (mimeType.startsWith('image/') || ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].contains(extension)) {
      type = 'image';
    } else if (mimeType.startsWith('audio/') || ['mp3', 'wav', 'aac', 'm4a', 'ogg'].contains(extension)) {
      type = 'audio';
    } else if (mimeType.startsWith('video/') || ['mp4', 'avi', 'mov', 'wmv', 'flv'].contains(extension)) {
      type = 'video';
    }

    switch (type) {
      case 'image':
        // For images, try to load from attachment data
        return FutureBuilder<Uint8List?>(
          future: _getAttachmentBytes(attachment),
          builder: (context, snapshot) {
            if (snapshot.hasData && snapshot.data != null) {
              return ClipRRect(
                borderRadius: BorderRadius.circular(7),
                child: Image.memory(
                  snapshot.data!,
                  fit: BoxFit.cover,
                  width: double.infinity,
                  height: double.infinity,
                  errorBuilder: (context, error, stackTrace) {
                    return Container(
                      color: const Color(0xFFF3F4F6),
                      child: const Icon(
                        Icons.broken_image_outlined,
                        size: 48,
                        color: Color(0xFF9CA3AF),
                      ),
                    );
                  },
                ),
              );
            } else {
              return Container(
                color: const Color(0xFFF3F4F6),
                child: const Icon(
                  Icons.image_outlined,
                  size: 48,
                  color: Color(0xFF9CA3AF),
                ),
              );
            }
          },
        );
      case 'audio':
        return Container(
          color: const Color(0xFFF3F4F6),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.audiotrack_outlined,
                size: 48,
                color: Color(0xFF6366F1), // indigo-500
              ),
              const SizedBox(height: 4),
              Text(
                extension.toUpperCase(),
                style: const TextStyle(
                  fontSize: 10,
                  fontWeight: FontWeight.w600,
                  color: Color(0xFF6366F1),
                ),
              ),
            ],
          ),
        );
      case 'video':
        return Container(
          color: const Color(0xFFF3F4F6),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.play_circle_outline,
                size: 48,
                color: Color(0xFF8B5CF6), // purple-500
              ),
              const SizedBox(height: 4),
              Text(
                extension.toUpperCase(),
                style: const TextStyle(
                  fontSize: 10,
                  fontWeight: FontWeight.w600,
                  color: Color(0xFF8B5CF6),
                ),
              ),
            ],
          ),
        );
      default:
        // Handle different document types with specific icons
        return Container(
          color: const Color(0xFFF3F4F6),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                _getDocumentIcon(extension),
                size: 48,
                color: _getDocumentColor(extension),
              ),
              const SizedBox(height: 4),
              Text(
                extension.isNotEmpty ? extension.toUpperCase() : 'FILE',
                style: TextStyle(
                  fontSize: 10,
                  fontWeight: FontWeight.w600,
                  color: _getDocumentColor(extension),
                ),
              ),
            ],
          ),
        );
    }
  }

  IconData _getDocumentIcon(String? extension) {
    if (extension == null) return Icons.description_outlined;

    switch (extension.toLowerCase()) {
      case 'pdf':
        return Icons.picture_as_pdf_outlined;
      case 'doc':
      case 'docx':
        return Icons.description_outlined;
      case 'xls':
      case 'xlsx':
        return Icons.table_chart_outlined;
      case 'ppt':
      case 'pptx':
        return Icons.slideshow_outlined;
      case 'txt':
        return Icons.text_snippet_outlined;
      case 'zip':
      case 'rar':
      case '7z':
        return Icons.folder_zip_outlined;
      default:
        return Icons.insert_drive_file_outlined;
    }
  }

  Color _getDocumentColor(String? extension) {
    if (extension == null) return const Color(0xFF6B7280);

    switch (extension.toLowerCase()) {
      case 'pdf':
        return const Color(0xFFDC2626); // red-600
      case 'doc':
      case 'docx':
        return const Color(0xFF2563EB); // blue-600
      case 'xls':
      case 'xlsx':
        return const Color(0xFF059669); // green-600
      case 'ppt':
      case 'pptx':
        return const Color(0xFFEA580C); // orange-600
      case 'txt':
        return const Color(0xFF6B7280); // gray-500
      case 'zip':
      case 'rar':
      case '7z':
        return const Color(0xFF7C3AED); // purple-600
      default:
        return const Color(0xFF6B7280); // gray-500
    }
  }

  Widget _buildEmptyAttachmentsState() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(40),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.attach_file_outlined,
            size: 64,
            color: Colors.grey.shade300,
          ),
          const SizedBox(height: 20),
          Text(
            'No Attachments',
            style: UIHelper.modernBodyStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Colors.grey.shade700,
            ),
          ),
          const SizedBox(height: 12),
          Text(
            'No files have been attached to this fault yet.',
            style: UIHelper.modernBodyStyle(
              fontSize: 14,
              color: Colors.grey.shade500,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          Text(
            'Switch to edit mode to add photos, documents, or other files.',
            style: TextStyle(
              fontFamily: 'Inter',
              fontSize: 13,
              color: Colors.grey.shade400,
              fontStyle: FontStyle.italic,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  // Removed _showAttachmentOptions - attachments are view-only

  // Removed _buildAttachmentOption - attachments are view-only

  void _initializeAttachments() async {
    // Load actual attachment data from fault header
    final faultHeader = ref.read(faultHeaderProvider);
    if (faultHeader.fault_id != null) {
      try {
        // Get fault documents for this specific fault
        await ref.read(getFaultDocumentProvider.notifier).getFaultDocuments(faultHeader.fault_id.toString());
        final allDocuments = ref.read(getFaultDocumentProvider);

        // Filter documents to only show those linked to this fault
        final faultDocuments = await DbHelper.getFaultDocumentByFaultId(faultHeader.fault_id.toString());
        final faultDocIds = faultDocuments.map((fd) => fd.doc_id).toSet();

        setState(() {
          _attachments = allDocuments.where((doc) => faultDocIds.contains(doc.doc_id)).toList();
        });
      } catch (e) {
        // Handle error silently and keep empty list
        setState(() {
          _attachments = [];
        });
      }
    } else {
      setState(() {
        _attachments = [];
      });
    }
  }

  Future<Uint8List?> _getAttachmentBytes(DOCUMENT_HEADER attachment) async {
    try {
      if (kIsWeb) {
        // For web, get from IndexedDB
        final base64String = await DbHelper().getAttachmentFromIndexDbByUid(attachment.doc_id ?? '');
        if (base64String != null && base64String.isNotEmpty) {
          return base64Decode(base64String);
        }
      } else {
        // For mobile, get from DOCUMENT_ATTACHMENT
        final docAttachment = await DbHelper.getDocumentAttachmentsByUid(attachment.doc_id ?? '');
        if (docAttachment?.local_path != null && docAttachment!.local_path!.isNotEmpty) {
          final file = File(docAttachment.local_path!);
          if (await file.exists()) {
            return await file.readAsBytes();
          }
        }
      }
    } catch (e) {
      // Handle error silently
    }
    return null;
  }

  // Removed attachment picking methods - attachments are view-only

  // Removed _addAttachment and _showErrorSnackBar - attachments are view-only

  void _previewAttachment(DOCUMENT_HEADER attachment) {
    final fileName = attachment.file_name ?? 'Unknown';
    final mimeType = attachment.mime_type ?? '';

    // Determine file type from mime type
    String type = 'document';
    if (mimeType.startsWith('image/')) {
      type = 'image';
    } else if (mimeType.startsWith('audio/')) {
      type = 'audio';
    } else if (mimeType.startsWith('video/')) {
      type = 'video';
    }

    showDialog(
      context: context,
      builder: (context) => Dialog(
        backgroundColor: Colors.black87,
        child: Container(
          constraints: const BoxConstraints(maxWidth: 600, maxHeight: 600),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Header
              Container(
                padding: const EdgeInsets.all(16),
                child: Row(
                  children: [
                    Expanded(
                      child: Text(
                        fileName,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                    IconButton(
                      onPressed: () => Navigator.pop(context),
                      icon: const Icon(Icons.close, color: Colors.white),
                    ),
                  ],
                ),
              ),
              // Content
              Expanded(
                child: Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(16),
                  child: _buildPreviewContent(type, attachment, fileName),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPreviewContent(String type, DOCUMENT_HEADER attachment, String name) {
    switch (type) {
      case 'image':
        return FutureBuilder<Uint8List?>(
          future: _getAttachmentBytes(attachment),
          builder: (context, snapshot) {
            if (snapshot.hasData && snapshot.data != null) {
              return InteractiveViewer(
                child: Image.memory(
                  snapshot.data!,
                  fit: BoxFit.contain,
                  errorBuilder: (context, error, stackTrace) {
                    return _buildPreviewError('Failed to load image');
                  },
                ),
              );
            } else {
              return _buildPreviewPlaceholder(Icons.image, 'Image Preview', name);
            }
          },
        );
      case 'audio':
        return _buildPreviewPlaceholder(Icons.audiotrack, 'Audio File', name);
      case 'video':
        return _buildPreviewPlaceholder(Icons.videocam, 'Video File', name);
      default:
        return _buildPreviewPlaceholder(Icons.description, 'Document', name);
    }
  }

  Widget _buildPreviewPlaceholder(IconData icon, String title, String name) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, size: 64, color: Colors.white70),
          const SizedBox(height: 16),
          Text(
            title,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            name,
            style: const TextStyle(
              color: Colors.white70,
              fontSize: 14,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildPreviewError(String message) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.error, size: 64, color: Colors.red),
          const SizedBox(height: 16),
          Text(
            message,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 16,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Future<void> _removeAttachment(String attachmentId) async {
    try {
      // Remove from database
      final attachment = _attachments.firstWhere((att) => att.doc_id == attachmentId);

      // Remove FAULT_DOCUMENT link
      final faultHeader = ref.read(faultHeaderProvider);
      if (faultHeader.fault_id != null) {
        final faultDocuments = await DbHelper.getFaultDocumentByFaultId(faultHeader.fault_id.toString());
        final faultDoc = faultDocuments.firstWhere((doc) => doc.doc_id == attachmentId);
        await AppDatabaseManager().delete(
          DBInputEntity(FAULT_DOCUMENT.TABLE_NAME, faultDoc.toJson())
        );
      }

      // Remove DOCUMENT_HEADER
      await AppDatabaseManager().delete(
        DBInputEntity(DOCUMENT_HEADER.TABLE_NAME, attachment.toJson())
      );

      // Remove DOCUMENT_ATTACHMENT
      final docAttachment = await DbHelper.getDocumentAttachmentsByUid(attachmentId);
      if (docAttachment != null) {
        await AppDatabaseManager().delete(
          DBInputEntity(DOCUMENT_ATTACHMENT.TABLE_NAME, docAttachment.toJson())
        );
      }

      // Remove from IndexedDB for web
      if (kIsWeb) {
        // Note: IndexedDB removal would need additional implementation
      }

      // Refresh attachments list
      _initializeAttachments();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Attachment removed'),
            backgroundColor: Colors.orange,
            duration: Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to remove attachment: $e'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }

  Widget _buildLightPriorityBadge(WidgetRef ref, FAULT_HEADER faultHeader) {
    // Use selected priority when editing, otherwise use fault header priority
    String priorityText = _isEditing ? _selectedPriority : 'Medium'; // Default fallback
    Color badgeColor;

    if (!_isEditing) {
      // Map priority from fault header to text and color
      if (faultHeader.priority != null) {
        try {
          int priorityInt = int.parse(faultHeader.priority.toString());
          switch (priorityInt) {
            case 1:
              priorityText = 'High';
              break;
            case 2:
              priorityText = 'Medium';
              break;
            case 3:
              priorityText = 'Low';
              break;
            default:
              priorityText = 'Medium';
          }
        } catch (e) {
          priorityText = 'Medium';
        }
      }
    }

    // Set color based on priority text
    switch (priorityText) {
      case 'High':
        badgeColor = const Color(0xFFDC2626); // Red
        break;
      case 'Medium':
        badgeColor = const Color(0xFFEA580C); // Orange
        break;
      case 'Low':
        badgeColor = const Color(0xFF059669); // Green
        break;
      default:
        badgeColor = const Color(0xFFEA580C); // Orange
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: badgeColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: badgeColor.withOpacity(0.3)),
      ),
      child: Text(
        priorityText,
        style: UIHelper.modernCaptionStyle(
          fontSize: 14, // Matching React Native text-sm (14px) for larger badges
          fontWeight: FontWeight.w600, // font-semibold equivalent
          color: badgeColor,
        ),
      ),
    );
  }

  Widget _buildLightStatusBadge(FAULT_HEADER faultHeader) {
    String statusText = UIHelper.getStatusString(faultHeader.status.toString());
    Color badgeColor;

    switch (statusText.toLowerCase()) {
      case 'open':
        badgeColor = const Color(0xFF2563EB); // Blue
        break;
      case 'assigned':
      case 'job assigned':
        badgeColor = const Color(0xFF7C3AED); // Purple
        break;
      case 'completed':
        badgeColor = const Color(0xFF059669); // Green
        break;
      default:
        badgeColor = const Color(0xFF6B7280); // Grey
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: badgeColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: badgeColor.withOpacity(0.3)),
      ),
      child: Text(
        statusText,
        style: UIHelper.modernCaptionStyle(
          fontSize: 14, // Matching React Native text-sm (14px) for larger badges
          fontWeight: FontWeight.w600, // font-semibold equivalent
          color: badgeColor,
        ),
      ),
    );
  }





  Widget _buildTabs(BuildContext context) {
    // Always enable attachments tab, but show different content based on state
    return Row(
      children: [
        // Details Tab
        GestureDetector(
          onTap: () {
            setState(() {
              _activeTab = 'details';
            });
          },
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 0, vertical: 8),
            decoration: BoxDecoration(
              border: Border(
                bottom: BorderSide(
                  color: _activeTab == 'details' ? const Color(0xFF2563EB) : Colors.transparent,
                  width: 2,
                ),
              ),
            ),
            child: Text(
              'Details',
              style: UIHelper.modernBodyStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: _activeTab == 'details' ? const Color(0xFF2563EB) : Colors.grey.shade600,
              ),
            ),
          ),
        ),
        const SizedBox(width: 24),
        // Attachments Tab - Always enabled
        GestureDetector(
          onTap: () {
            setState(() {
              _activeTab = 'attachments';
            });
          },
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 0, vertical: 8),
            decoration: BoxDecoration(
              border: Border(
                bottom: BorderSide(
                  color: _activeTab == 'attachments' ? const Color(0xFF2563EB) : Colors.transparent,
                  width: 2,
                ),
              ),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  'Attachments',
                  style: UIHelper.modernBodyStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: _activeTab == 'attachments' ? const Color(0xFF2563EB) : Colors.grey.shade600,
                  ),
                ),
                const SizedBox(width: 6),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                  decoration: BoxDecoration(
                    color: _activeTab == 'attachments'
                        ? const Color(0xFF2563EB).withOpacity(0.1)
                        : Colors.grey.shade200,
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: Text(
                    '${_attachments.length}',
                    style: UIHelper.modernCaptionStyle(
                      fontSize: 11,
                      fontWeight: FontWeight.w600,
                      color: _activeTab == 'attachments'
                          ? const Color(0xFF2563EB)
                          : Colors.grey.shade700,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildFaultAttributes(BuildContext context, WidgetRef ref, FAULT_HEADER faultHeader) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              Icons.list_alt,
              size: 16,
              color: Colors.grey.shade600,
            ),
            const SizedBox(width: 8),
            Text(
              'Fault Attributes',
              style: UIHelper.modernBodyStyle(
                fontSize: 18, // Matching React Native text-lg (18px) for section titles
                fontWeight: FontWeight.w600, // font-semibold equivalent
                color: AppColors.modernPrimaryText,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Container(
          height: 1,
          width: double.infinity,
          color: Colors.grey.shade200,
        ),
        const SizedBox(height: 16),

        if (_isEditing) ...[
          // Edit mode - Priority button group
          _buildFormField(
            'Priority',
            _buildPriorityButtonGroup(),
          ),
          const SizedBox(height: 16),

          // Two column layout for dropdowns
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                child: _buildFormField(
                  'Fault Type',
                  _buildFaultTypeDropdown(ref),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildFormField(
                  'Fault Mode',
                  _buildFaultModeDropdown(ref),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // Two column layout for dates
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                child: _buildFormField(
                  'Fault Noticed On',
                  _buildDateField(_faultNoticedOnController),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildFormField(
                  'Due Date',
                  _buildDateField(_dueOnController),
                ),
              ),
            ],
          ),
        ] else ...[
          // Display mode
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildAttributeField('Fault Type', _getFaultType(ref, faultHeader)),
                    const SizedBox(height: 16),
                    _buildAttributeField('Fault Noticed On', _getFormattedDate(faultHeader.reported_on)),
                  ],
                ),
              ),
              const SizedBox(width: 32),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildAttributeField('Fault Mode', _getFaultMode(ref, faultHeader)),
                    const SizedBox(height: 16),
                    _buildAttributeField('Due Date', _getFormattedDate(faultHeader.req_end)),
                  ],
                ),
              ),
            ],
          ),
        ],
      ],
    );
  }

  Widget _buildContext(BuildContext context, WidgetRef ref, FAULT_HEADER faultHeader) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              Icons.location_on_outlined,
              size: 16,
              color: Colors.grey.shade600,
            ),
            const SizedBox(width: 8),
            Text(
              'Context',
              style: UIHelper.modernBodyStyle(
                fontSize: 18, // Matching React Native text-lg (18px) for section titles
                fontWeight: FontWeight.w600, // font-semibold equivalent
                color: AppColors.modernPrimaryText,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Container(
          height: 1,
          width: double.infinity,
          color: Colors.grey.shade200,
        ),
        const SizedBox(height: 16),
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildAttributeField('Functional Location', _getLocationIdDescription(ref, faultHeader)),
                  const SizedBox(height: 16),
                  _buildAttributeField('Reported From', _getReportedFrom(faultHeader)),
                ],
              ),
            ),
            const SizedBox(width: 32),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildAttributeField('Asset', _getAsset(ref, faultHeader)),
                  const SizedBox(height: 32),
                  _buildAttributeField('Reported By', _getReportedBy(faultHeader)),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildLongText(BuildContext context, FAULT_HEADER faultHeader) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              Icons.description_outlined,
              size: 16,
              color: Colors.grey.shade600,
            ),
            const SizedBox(width: 8),
            Text(
              'Long Text',
              style: UIHelper.modernBodyStyle(
                fontSize: 18, // Matching React Native text-lg (18px) for section titles
                fontWeight: FontWeight.w600, // font-semibold equivalent
                color: AppColors.modernPrimaryText,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Container(
          height: 1,
          width: double.infinity,
          color: Colors.grey.shade200,
        ),
        const SizedBox(height: 16),

        if (_isEditing) ...[
          // Edit mode - TextArea
          TextField(
            controller: _longTextController,
            maxLines: 4,
            enabled: FaultStatusHelper.shouldShowEditButton(faultHeader), // Disable for read-only faults
            decoration: InputDecoration(
              hintText: 'Detailed notes about the fault...',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(6),
                borderSide: BorderSide(color: Colors.grey.shade300),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(6),
                borderSide: const BorderSide(color: Color(0xFF2563EB)),
              ),
              disabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(6),
                borderSide: BorderSide(color: Colors.grey.shade200),
              ),
              fillColor: FaultStatusHelper.shouldShowEditButton(faultHeader)
                  ? Colors.white
                  : Colors.grey.shade50,
              filled: true,
              contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            ),
            style: UIHelper.modernBodyStyle(
              fontSize: 14,
              fontWeight: FontWeight.w400,
              color: FaultStatusHelper.shouldShowEditButton(faultHeader)
                  ? AppColors.modernPrimaryText
                  : Colors.grey.shade600,
            ),
          ),
        ] else
          Text(
            faultHeader.details ?? 'No additional details available.',
            style: UIHelper.modernBodyStyle(
              fontSize: 14,
              fontWeight: FontWeight.w400,
              color: AppColors.modernPrimaryText,
            ),
          ),
      ],
    );
  }

  Widget _buildAttributeField(String label, String value) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: UIHelper.modernCaptionStyle(
            fontSize: 11,
            fontWeight: FontWeight.w500,
            color: Colors.grey.shade600,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: UIHelper.modernBodyStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: AppColors.modernPrimaryText,
          ),
        ),
      ],
    );
  }

  // Helper methods for getting data
  String _getFaultType(WidgetRef ref, FAULT_HEADER faultHeader) {
    if (faultHeader.fault_type != null) {
      return ref
          .watch(faultTypeListProvider.notifier)
          .fetchFaultTypeCode(faultHeader.fault_type.toString());
    }
    return 'N/A';
  }

  String _getFaultMode(WidgetRef ref, FAULT_HEADER faultHeader) {
    if (faultHeader.failure_mode != null) {
      return ref
          .watch(faultModeHeaderListProvider.notifier)
          .fetchFaultModeByCode(faultHeader.failure_mode.toString());
    }
    return 'N/A';
  }

  String _getFormattedDate(int? timestamp) {
    if (timestamp != null) {
      // The timestamp is in YYYYMMDD format, not milliseconds
      return UIHelper.formatDate(timestamp.toString());
    }
    return 'N/A';
  }

  String _getLocationIdDescription(WidgetRef ref, FAULT_HEADER faultHeader) {
    if (faultHeader.location_id != null) {
      final flocList = ref.watch(flocHeaderProvider);
      try {
        final floc = flocList.firstWhere(
          (element) => element.location_id == faultHeader.location_id,
        );
        String locationId = faultHeader.location_id.toString();
        String locationDesc = floc.description ?? 'N/A';
        return '$locationId - $locationDesc';
      } catch (e) {
        return faultHeader.location_id.toString();
      }
    }
    return 'N/A';
  }

  String _getAsset(WidgetRef ref, FAULT_HEADER faultHeader) {
    if (faultHeader.asset_no != null) {
      final assetList = ref.watch(assetHeaderProvider);
      try {
        final asset = assetList.firstWhere(
          (element) => element.asset_no == faultHeader.asset_no,
        );
        String assetId = faultHeader.asset_no.toString();
        String assetDesc = asset.description ?? 'N/A';
        return '$assetId - $assetDesc';
      } catch (e) {
        return faultHeader.asset_no.toString();
      }
    }
    return 'N/A';
  }

  String _getReportedFrom(FAULT_HEADER faultHeader) {
    // This might need to be mapped from a specific field or provider
    return 'Hydraulic System Maintenance Plan';
  }

  String _getReportedBy(FAULT_HEADER faultHeader) {
    if (faultHeader.reported_by != null) {
      return faultHeader.reported_by.toString();
    }
    return 'N/A';
  }

  /// Builds a consistent action button widget
  Widget _buildActionButton(String label, IconData icon, Color color, VoidCallback onTap) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        decoration: BoxDecoration(
          color: color,
          borderRadius: BorderRadius.circular(6),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              size: 16,
              color: Colors.white,
            ),
            const SizedBox(width: 4),
            Text(
              label,
              style: UIHelper.modernBodyStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Colors.white,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Handles Create Job action - directly navigate to job creation without confirmation
  void _handleCreateJob(BuildContext context, WidgetRef ref, FAULT_HEADER faultHeader) {
    // Navigate directly to job creation screen without confirmation popup
    _navigateToJobCreationScreen(faultHeader, ref);
  }

  /// Handles Complete action - integrates with existing fault completion logic
  void _handleComplete(BuildContext context, WidgetRef ref, FAULT_HEADER faultHeader) {
    // Use the existing logic from fault_detail_screen.dart
    _handleStatusActionButtonsFunctions('complete', faultHeader, context, ref);
  }

  /// Handles status action button functions (Create Job and Complete)
  /// This is adapted from the existing fault_detail_screen.dart implementation
  void _handleStatusActionButtonsFunctions(String type, FAULT_HEADER faultHeader,
      BuildContext context, WidgetRef ref) {
    if (type == 'complete') {
      UIHelper.showConfirmationDialogWithYesOrNo(context,
          description: AppLocalizations.of(context)!
              .do_you_want_to_complete_fault, yes: () {
        _onYes(type, faultHeader, Constants.FAULT_STATE_NOCO, context, ref);
      }, no: () {
        Navigator.pop(context);
      });
    }
  }

  /// Handles the "Yes" action for status changes
  /// This is adapted from the existing fault_detail_screen.dart implementation
  void _onYes(String type, FAULT_HEADER faultHeader, String status,
      BuildContext context, WidgetRef ref) async {
    Navigator.pop(context); // Close confirmation dialog

    final faultHeaderData = ref.read(faultHeaderProvider.notifier);
    FAULT_HEADER header = faultHeader;

    // Update fault header in database
    await AppDatabaseManager()
        .update(DBInputEntity(FAULT_HEADER.TABLE_NAME, header.toJson()));

    // Refresh fault header data
    await faultHeaderData.getFaultHeader(
        faultId: header.fault_id.toString());

    // Update or create fault action
    FAULT_ACTION? action = await DbHelper.getFaultActionByFaultId(header.fault_id.toString());
    if (action != null) {
      action.user_action = status;
      await AppDatabaseManager()
          .update(DBInputEntity(FAULT_ACTION.TABLE_NAME, action.toJson()));
    } else {
      FAULT_ACTION newAction = FAULT_ACTION(
          fault_id: header.fault_id.toString(), user_action: status);
      newAction.fid = header.lid;
      await AppDatabaseManager()
          .insert(DBInputEntity(FAULT_ACTION.TABLE_NAME, newAction.toJson()));
    }

    if (type == 'assign') {
      // Navigate to job creation screen
      var result = await _navigateToJobCreationScreen(faultHeader, ref);
      if (result != null) {
        final jobHeaderData = ref.read(jobHeaderProvider);
        header.job_id = jobHeaderData.job_id;
        await AppDatabaseManager()
            .insert(DBInputEntity(FAULT_HEADER.TABLE_NAME, header.toJson()));
        await faultHeaderData.getFaultHeader(
            faultId: faultHeader.fault_id.toString());

        // Update fault action for job assignment
        FAULT_ACTION? assignAction = await DbHelper.getFaultActionByFaultId(header.fault_id.toString());
        if (assignAction != null) {
          assignAction.user_action = status;
          await AppDatabaseManager()
              .update(DBInputEntity(FAULT_ACTION.TABLE_NAME, assignAction.toJson()));
        } else {
          FAULT_ACTION assignAction = FAULT_ACTION(
              fault_id: header.fault_id.toString(), user_action: status);
          assignAction.fid = header.lid;
          await AppDatabaseManager()
              .insert(DBInputEntity(FAULT_ACTION.TABLE_NAME, assignAction.toJson()));
        }

        if (mounted) {
          _sendToServer('assign', faultHeader, context);
        }
      }
    } else {
      if (mounted) {
        await _sendToServer(type, faultHeader, context);
      }
    }
  }

  /// Navigate to job creation screen
  /// This implements the complete job creation logic using the modern job creation modal
  Future<dynamic> _navigateToJobCreationScreen(FAULT_HEADER faultHeader, WidgetRef ref) async {
    return await showDialog(
      context: context,
      builder: (context) => ModernJobCreationModal(
        sourceFault: faultHeader,
        onClose: () => Navigator.pop(context, null),
        onJobCreated: (jobHeader) async {
          Navigator.pop(context, 'success');

          // Use the same refresh logic as the Complete action for consistency
          // Refresh the individual fault header to update the detail view
          final faultHeaderData = ref.read(faultHeaderProvider.notifier);
          await faultHeaderData.getFaultHeader(faultId: faultHeader.fault_id.toString());

          // Refresh the fault list to update the UI with new status
          final plant = ref.read(plantProvider);
          final faultHeaderList = ref.read(faultHeaderListProvider.notifier);
          await faultHeaderList.fetchFaultHeaderList(plant);

          // Also refresh filtered list if filters are applied (same as Complete action)
          final filteredFaultType = ref.read(filteredFaultHeaderListProvider.notifier);
          final filterOfFaultCode = ref.read(filterOfFaultCodeProvider);
          final filterOfPriorityCode = ref.read(filterOfFaultPriorityCodeProvider);
          final statusTypeFilter = ref.read(statusTypeFaultFilterProvider);
          final plantSection = ref.read(plantSectionProvider);
          final search = ref.read(searchTextProvider);

          // Apply filters if any are active
          if ((search != '') ||
              filterOfFaultCode.isNotEmpty ||
              filterOfPriorityCode.isNotEmpty ||
              statusTypeFilter.isNotEmpty) {
            await filteredFaultType.filteredFaultHeaderList(
                faulttypeList: filterOfFaultCode,
                priorityList: filterOfPriorityCode,
                statusList: statusTypeFilter,
                type: (search != '') ? AppConstants.search : AppConstants.faultType,
                plantId: plant,
                plantSec: plantSection,
                search: search);
          } else {
            final currentFaultList = ref.read(faultHeaderListProvider);
            await filteredFaultType.filteredFaultHeaderList(
                type: 'Initial',
                faultList: currentFaultList,
                plantId: plant,
                plantSec: plantSection);
          }

          // Show success message
          if (context.mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('Job ${jobHeader.job_id} created successfully'),
                backgroundColor: Colors.green,
              ),
            );
          }
        },
      ),
    );
  }

  /// Send data to server
  /// This implements the complete server sync logic from fault_detail_screen.dart
  Future<void> _sendToServer(String type, FAULT_HEADER faultHeader, BuildContext context) async {
    if (type == AppConstants.assign) {
      UIHelper().progressDialog(
          context: context,
          message: AppLocalizations.of(context)!.assigning_job);
    } else if (type == AppConstants.complete) {
      UIHelper().progressDialog(
          context: context,
          message: AppLocalizations.of(context)!.completing_fault);
    }

    try {
      // Call the appropriate PA Helper method for server sync
      if (!kIsWeb) {
        await PAHelper.addOrModifyFaultInAsyncMode(context, faultHeader);
      } else {
        await PAHelper.addOrModifyFaultInSyncMode(context, faultHeader);
      }

      if (mounted) {
        // Close progress dialog
        Navigator.pop(context);

        // Refresh the individual fault header to update the detail view
        final faultHeaderData = ref.read(faultHeaderProvider.notifier);
        await faultHeaderData.getFaultHeader(faultId: faultHeader.fault_id.toString());

        // Refresh the fault list to update the UI with new status
        final plant = ref.read(plantProvider);
        final faultHeaderList = ref.read(faultHeaderListProvider.notifier);
        await faultHeaderList.fetchFaultHeaderList(plant);

        // Also refresh filtered list if filters are applied
        final filteredFaultType = ref.read(filteredFaultHeaderListProvider.notifier);
        final filterOfFaultCode = ref.read(filterOfFaultCodeProvider);
        final filterOfPriorityCode = ref.read(filterOfFaultPriorityCodeProvider);
        final statusTypeFilter = ref.read(statusTypeFaultFilterProvider);
        final plantSection = ref.read(plantSectionProvider);
        final search = ref.read(searchTextProvider);

        // Apply filters if any are active
        if ((search != '') ||
            filterOfFaultCode.isNotEmpty ||
            filterOfPriorityCode.isNotEmpty ||
            statusTypeFilter.isNotEmpty) {
          await filteredFaultType.filteredFaultHeaderList(
              faulttypeList: filterOfFaultCode,
              priorityList: filterOfPriorityCode,
              statusList: statusTypeFilter,
              type: (search != '') ? AppConstants.search : AppConstants.faultType,
              plantId: plant,
              plantSec: plantSection,
              search: search);
        } else {
          final currentFaultList = ref.read(faultHeaderListProvider);
          await filteredFaultType.filteredFaultHeaderList(
              type: 'Initial',
              faultList: currentFaultList,
              plantId: plant,
              plantSec: plantSection);
        }

        // Show success message
        String message = type == AppConstants.assign
            ? 'Job assigned successfully'
            : 'Fault completed successfully';

        if (mounted) {
          UIHelper.showResultInfoDialog(
            context,
            description: message,
            onPressed: () => Navigator.pop(context),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        // Close progress dialog
        Navigator.pop(context);

        // Show error message
        UIHelper.showResultInfoDialog(
          context,
          description: 'Error: ${e.toString()}',
          onPressed: () => Navigator.pop(context),
        );
      }
    }
  }
}

// Custom painter for dotted border
class DottedBorderPainter extends CustomPainter {
  final Color color;
  final double strokeWidth;
  final double dashLength;
  final double gapLength;
  final double borderRadius;

  DottedBorderPainter({
    required this.color,
    required this.strokeWidth,
    required this.dashLength,
    required this.gapLength,
    required this.borderRadius,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..strokeWidth = strokeWidth
      ..style = PaintingStyle.stroke;

    final path = Path()
      ..addRRect(RRect.fromRectAndRadius(
        Rect.fromLTWH(strokeWidth / 2, strokeWidth / 2,
                     size.width - strokeWidth, size.height - strokeWidth),
        Radius.circular(borderRadius),
      ));

    _drawDashedPath(canvas, path, paint);
  }

  void _drawDashedPath(Canvas canvas, Path path, Paint paint) {
    final pathMetrics = path.computeMetrics();
    for (final pathMetric in pathMetrics) {
      double distance = 0.0;
      while (distance < pathMetric.length) {
        final nextDistance = distance + dashLength;
        final extractPath = pathMetric.extractPath(
          distance,
          nextDistance > pathMetric.length ? pathMetric.length : nextDistance,
        );
        canvas.drawPath(extractPath, paint);
        distance = nextDistance + gapLength;
      }
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

// Helper function to get file extension
String _getFileExtension(String fileName) {
  final lastDot = fileName.lastIndexOf('.');
  if (lastDot != -1 && lastDot < fileName.length - 1) {
    return fileName.substring(lastDot + 1).toLowerCase();
  }
  return '';
}

// Removed _getMimeType helper - attachments are view-only
