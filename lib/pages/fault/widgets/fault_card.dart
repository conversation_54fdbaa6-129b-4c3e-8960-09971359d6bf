import 'dart:convert';

import 'package:collection/collection.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import 'package:intl/intl.dart';
import 'package:logger/Logger.dart';
import 'package:rounds/be/FAULT_HEADER.dart';
import 'package:rounds/models/intractive_Item_Model.dart';
import 'package:rounds/pages/fault/fault_detail_screen.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:rounds/pages/fault/tabs/edit_fault_field_provider.dart';
import 'package:rounds/utils/app_constants.dart';
import 'package:rounds/utils/utils.dart';
import 'package:unvired_sdk/unvired_sdk.dart';
import '../../../be/DOCUMENT_HEADER.dart';
import '../../../be/FAULT_ACTION.dart';
import '../../../be/FAULT_DOCUMENT.dart';
import '../../../be/JOB_HEADER.dart';
import '../../../be/USER_HEADER.dart';
import '../../../helpers/db_helper.dart';
import '../../../helpers/pa_helper.dart';
import '../../../helpers/ui_helper.dart';
import '../../../providers/attachments/attachment_provider.dart';
import '../../../providers/fault/fault_header_provider.dart';
import '../../../providers/fault/fault_type_provider.dart';
import '../../../providers/job_creation/job_header_provider.dart';
import '../../../providers/user_provider.dart';
import '../../../providers/assets/asset_provider.dart';
import '../../../providers/assets/floc_provider.dart';
import '../../../utils/app_colors.dart';
import '../../../utils/constants.dart';
import '../../../widgets/cilt_indicator.dart';
import '../../job_creation/job_creation_detail_screen.dart';
import '../../job_creation/tabs/edit_job_creation_field_provider.dart';
import '../fault_filter_provider.dart';
import '../fault_screen.dart';
import 'package:unvired_sdk/src/helper/url_service.dart';

class FaultCard extends ConsumerStatefulWidget {
  final int index;
  final Color color;
  final FAULT_HEADER item;
  final Function(InteractiveItemModel) onTap;

  const FaultCard(
      {super.key,
      required this.index,
      this.color = Colors.white,
      required this.item,
      required this.onTap});

  @override
  _FaultCardState createState() => _FaultCardState();
}

class _FaultCardState extends ConsumerState<FaultCard> {
  static const sourceClass = 'FaultCard';

  @override
  void initState() {
    super.initState();
/*    if (kIsWeb) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        autoSelectIndexOne();
      });
    }*/
  }

  void autoSelectIndexOne() async {
    final plant = ref.watch(plantProvider.notifier).state;
    final faultHeaderList = ref.watch(faultHeaderListProvider.notifier);
    final faultHeaderProviderr = ref.watch(faultHeaderProvider.notifier);

    final faultAction = ref.watch(getFaultActionProvider.notifier);
    final faultDocument = ref.watch(getFaultDocumentProvider.notifier);
    final documentAttachmentProviderData =
        ref.watch(documentAttachmentProvider.notifier);
    final faultNoticedOn = ref.watch(faultNoticedOnProvider.notifier);
    final dueOn = ref.watch(faultDueOnProvider.notifier);

    final faultDescription = ref.watch(faultDescriptionProvider.notifier);
    final faultLongText = ref.watch(faultLongTextProvider.notifier);
    final faultMode = ref.watch(faultModeHeaderProvider.notifier);
    final faultPriority = ref.watch(priorityProvider.notifier);
    final faultType = ref.watch(faultTypeProvider.notifier);
    final assetLocList = ref.read(assetLocListProvider.notifier);

    if (ScreenType.desktop == UIHelper().getScreenType(context)) {
      if (widget.index == 0) {
        if (widget.item.status != null) {
          if (widget.item.reported_on != null) {
            faultNoticedOn.getFaultNoticedOn(widget.item.reported_on!);
          }
          if (widget.item.req_end != null) {
            dueOn.getDueOn(widget.item.req_end!);
          }
        }
        if (widget.item.description != null) {
          faultDescription
              .getFaultDescription(widget.item.description.toString());
        }
        if (widget.item.failure_mode != null) {
          faultMode.getFaultMode(ref
              .read(faultModeHeaderListProvider.notifier)
              .fetchFaultModeByCode(widget.item.failure_mode.toString()));
        }
        if (widget.item.priority != null) {
          faultPriority.getPriority(ref
              .read(priorityListProvider.notifier)
              .fetchPriorityCode(widget.item.priority.toString()));
        }
        if (widget.item.fault_type != null) {
          faultType.getFaultType(ref
              .watch(faultTypeListProvider.notifier)
              .fetchFaultTypeCode(widget.item.fault_type.toString()));
        }
        if (widget.item.details != null) {
          faultLongText.getFaultLongText(widget.item.details.toString());
        }
        if (widget.item.asset_no != null) {
          await assetLocList
              .fetchAssetLocList(widget.item.location_id.toString());
        }

        FAULT_HEADER? dummyData;
        dummyData = await DbHelper.getFaultHeaderByFaultId(
            widget.item.fault_id.toString());
        if (dummyData != null) {
          await faultHeaderProviderr.getFaultHeader(
              faultId: widget.item.fault_id.toString());
          await faultAction.getFaultAction(widget.item.fault_id.toString());
          await faultDocument
              .getFaultDocuments(widget.item.fault_id.toString());
        } else {
          await faultHeaderProviderr.getFaultHeader(data: widget.item);
          await faultAction.getFaultAction(widget.item.fault_id.toString());
          await faultDocument
              .getFaultDocuments(widget.item.fault_id.toString());
        }
        await documentAttachmentProviderData.fetchDocumentAttachments();
        if (mounted) {
          if (UIHelper().getScreenType(context) != ScreenType.desktop) {
            await Navigator.push(context, MaterialPageRoute(builder: (context) {
              return const FaultDetailScreen(
                type: AppConstants.fault,
              );
            }));
          } else {
            widget.onTap(InteractiveItemModel(
                type: "FAULT_HEADER",
                data: {"type": AppConstants.fault, "index": widget.index}));
          }
        }
        await faultHeaderList.fetchFaultHeaderList(plant);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return getCard();
  }

  Widget getCard() {
    if (ScreenType.desktop == UIHelper().getScreenType(context)) {
      return getFaultCard(context);
    } else {
      return _getFaultSlidableCard(context);
    }
  }

  _getFaultSlidableCard(BuildContext context) {
    return Slidable(
      groupTag: AppLocalizations.of(context)!.faults ?? "",
      endActionPane: ActionPane(
        motion: const ScrollMotion(),
        children: isExecutionVisible()
            ? getFaultRightStatusActionButtons(widget.item)
            : [
                SlidableAction(
                  onPressed: (context) {},
                  label: '',
                )
              ],
      ),
      startActionPane: ActionPane(
          motion: const ScrollMotion(),
          children: isExecutionVisible()
              ? getFaultLeftStatusActionButtons(widget.item)
              : [
                  SlidableAction(
                    onPressed: (context) {},
                    label: '',
                  )
                ]),
      child: getFaultCard(context),
    );
  }

  clearFaultStates() {
    final faultHeader = ref.read(faultHeaderProvider.notifier);
    final location = ref.watch(locationProvider.notifier);
    final assetLocList = ref.watch(assetLocListProvider.notifier);
    final asset = ref.watch(assetProvider.notifier);
    final dueOn = ref.watch(faultDueOnProvider.notifier);
    final description = ref.watch(faultDescriptionProvider.notifier);
    final faultMode = ref.watch(faultModeHeaderProvider.notifier);
    final priority = ref.watch(priorityProvider.notifier);
    final faultType = ref.watch(faultTypeProvider.notifier);
    final longText = ref.watch(faultLongTextProvider.notifier);
    final reportedBy = ref.watch(faultReportedByProvider.notifier);
    final originalFaultHeader = ref.read(faultHeaderProvider);
    location.clearLocation();
    assetLocList.clearAssetLocList();
    asset.clearAsset();
    dueOn.clearDueOn();
    description.clearFaultDescription();
    faultMode.clearFaultMode();
    priority.clearPriority();
    faultType.clearFaultType();
    longText.clearFaultLongText();
    reportedBy.clearFaultReportedBy();
    faultHeader.clearFault();
    //reset the fault state with orginal fault
    faultHeader.resetFault(originalFaultHeader);
    // description.getFaultDescription(originalFaultHeader.description.toString());
    // // faultNoticedOn.getFaultNoticedOn(originalFaultHeader.reported_on!);
    // dueOn.getDueOn(originalFaultHeader.req_end!);
    // faultMode.getFaultMode(ref
    //     .read(faultModeHeaderListProvider.notifier)
    //     .fetchFaultModeByCode(originalFaultHeader.failure_mode.toString()));
    // priority.getPriority(ref
    //     .read(priorityListProvider.notifier)
    //     .fetchPriorityCode(originalFaultHeader.priority.toString()));
    // faultType.getFaultType(ref
    //     .watch(faultTypeListProvider.notifier)
    //     .fetchFaultTypeCode(originalFaultHeader.fault_type.toString()));
    // longText.getFaultLongText(originalFaultHeader.details.toString());
    // assetLocList.fetchAssetLocList(originalFaultHeader.location_id.toString());
  }

  Widget getFaultCard(BuildContext context) {
    final plant = ref.watch(plantProvider.notifier).state;
    final plantSection = ref.watch(plantSectionProvider.notifier).state;
    final faultHeaderList = ref.watch(faultHeaderListProvider.notifier);
    final ciltHeader = ref.watch(getCiltHeaderByFaultHeaderProvider.notifier);
    final faultHeaderProviderr = ref.watch(faultHeaderProvider.notifier);
    final editProviderr = ref.watch(editFaultFieldProvider.notifier);
    final faultAction = ref.watch(getFaultActionProvider.notifier);
    final faultDocument = ref.watch(getFaultDocumentProvider.notifier);
    final documentAttachmentProviderData =
        ref.watch(documentAttachmentProvider.notifier);
    final faultNoticedOn = ref.watch(faultNoticedOnProvider.notifier);
    final dueOn = ref.watch(faultDueOnProvider.notifier);
    final userList = ref.watch(usersListProvider);

    final faultDescription = ref.watch(faultDescriptionProvider.notifier);
    final faultLongText = ref.watch(faultLongTextProvider.notifier);
    final faultMode = ref.watch(faultModeHeaderProvider.notifier);
    final faultPriority = ref.watch(priorityProvider.notifier);
    final faultType = ref.watch(faultTypeProvider.notifier);
    final assetLocList = ref.read(assetLocListProvider.notifier);
    USER_HEADER? reportedBy;
    if (widget.item.reported_by != null) {
      reportedBy = DbHelper.getAssignUser(userList, (widget.item.reported_by!));
    } else {
      reportedBy = USER_HEADER(user_id: null);
    }
    return InkWell(
      onTap: !isDisplayVisible()
          ? () {}
          : () async {
              bool data = await checkIfModifiedDataThere();
              if (!data) {
                if (mounted) {
                  UIHelper.showConfirmationDialogWithYesOrNo(context,
                      description: AppLocalizations.of(context)!
                          .you_have_unsaved_data, no: () {
                    onUnsaveFault(faultHeaderProviderr.state);
                  }, yes: () {
                    Navigator.pop(context);
                    onCheck(faultHeaderProviderr.state.status, willScope: true);
                  });
                }
              } else {
                clearFaultStates();
                if (widget.item.cilt_task_id != null) {
                  await ciltHeader.getCiltPlanHeaderByFaultHeader(widget.item);
                }
                editProviderr.getEditFaultFieldEnable(false);
                if (widget.item.status != null) {
                  if (widget.item.reported_on != null) {
                    faultNoticedOn.getFaultNoticedOn(widget.item.reported_on!);
                  }
                  if (widget.item.req_end != null) {
                    dueOn.getDueOn(widget.item.req_end!);
                  }
                }
                if (widget.item.description != null) {
                  faultDescription
                      .getFaultDescription(widget.item.description.toString());
                }
                if (widget.item.failure_mode != null) {
                  faultMode.getFaultMode(ref
                      .read(faultModeHeaderListProvider.notifier)
                      .fetchFaultModeByCode(
                          widget.item.failure_mode.toString()));
                }
                if (widget.item.priority != null) {
                  faultPriority.getPriority(ref
                      .read(priorityListProvider.notifier)
                      .fetchPriorityCode(widget.item.priority.toString()));
                }
                if (widget.item.fault_type != null) {
                  faultType.getFaultType(ref
                      .watch(faultTypeListProvider.notifier)
                      .fetchFaultTypeCode(widget.item.fault_type.toString()));
                }
                if (widget.item.details != null) {
                  faultLongText
                      .getFaultLongText(widget.item.details.toString());
                }
                if (widget.item.asset_no != null) {
                  await assetLocList
                      .fetchAssetLocList(widget.item.location_id.toString());
                }

                FAULT_HEADER? dummyData;
                dummyData = await DbHelper.getFaultHeaderByFaultId(
                    widget.item.fault_id.toString());
                if (dummyData != null) {
                  // if (!kIsWeb) {
                  await faultHeaderProviderr.getFaultHeader(
                      faultId: widget.item.fault_id.toString());
                  await faultAction
                      .getFaultAction(widget.item.fault_id.toString());
                  await faultDocument
                      .getFaultDocuments(widget.item.fault_id.toString());
                  // }
                } else {
                  await faultHeaderProviderr.getFaultHeader(data: widget.item);
                  await faultAction
                      .getFaultAction(widget.item.fault_id.toString());
                  await faultDocument
                      .getFaultDocuments(widget.item.fault_id.toString());
                }
                await documentAttachmentProviderData.fetchDocumentAttachments();

                if (mounted) {
                  if (UIHelper().getScreenType(context) != ScreenType.desktop) {
                    await Navigator.push(context,
                        MaterialPageRoute(builder: (context) {
                      return const FaultDetailScreen(
                        type: AppConstants.fault,
                      );
                    }));
                  } else {
                    widget.onTap(InteractiveItemModel(
                        type: "FAULT_HEADER",
                        data: {
                          "type": AppConstants.fault,
                          "index": widget.index
                        }));
                  }
                }
                await faultHeaderList.fetchFaultHeaderList(plant);
              }
            },
      child: Container(
        decoration: BoxDecoration(
          color: (UIHelper().getScreenType(context) == ScreenType.desktop &&
                  widget.color != AppColors.modernCardBackground)
              ? AppColors.primaryColor.withOpacity(0.05)
              : AppColors.modernCardBackground,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: (UIHelper().getScreenType(context) == ScreenType.desktop &&
                    widget.color != AppColors.modernCardBackground)
                ? AppColors.primaryColor.withOpacity(0.3)
                : Colors.grey.shade300,
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Padding(
          padding: const EdgeInsets.all(12.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Row 1: Fault number on left, Priority badge on right
              Row(
                children: [
                  Text(
                    widget.item.status == null
                        ? 'NEW'
                        : widget.item.fault_id.toString(),
                    style: UIHelper.modernTitleStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w700,
                      color: AppColors.modernPrimaryText,
                    ),
                  ),
                  const Spacer(),
                  _buildPriorityBadge(),
                ],
              ),
              const SizedBox(height: 6),

              // Row 2: Fault description
              Text(
                widget.item.description?.toString() ?? 'No description',
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
                style: UIHelper.modernBodyStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: AppColors.modernPrimaryText,
                ),
              ),
              const SizedBox(height: 6),

              // Row 3: Asset/Equipment icon + text on left + Fault type badge on right
              Row(
                children: [
                  Icon(
                    _getAssetLocationIcon(),
                    size: 12,
                    color: AppColors.modernSecondaryText,
                  ),
                  const SizedBox(width: 4),
                  Expanded(
                    child: Text(
                      _getAssetDescription(),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                      style: UIHelper.modernBodyStyle(
                        fontSize: 11,
                        color: AppColors.modernSecondaryText,
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  _buildFaultTypeBadge(),
                ],
              ),
              const SizedBox(height: 6),

              // Row 4: Date on left + Assignment status badge on right
              Row(
                children: [
                  Text(
                    _getFormattedDate(widget.item.reported_on),
                    style: UIHelper.modernCaptionStyle(
                      fontSize: 10,
                      color: AppColors.modernTertiaryText,
                    ),
                  ),
                  const Spacer(),
                  _buildAssignmentStatusBadge(),
                ],
              ),
              if (widget.item.syncStatus.index == 1 ||
                  widget.item.syncStatus.index == 2 ||
                  widget.item.syncStatus.index == 3)
                Center(
                    child: ElevatedButton(
                  style: ElevatedButton.styleFrom(
                      backgroundColor: getSyncStatusColor(widget.item)),
                  onPressed: () async {
                    if (widget.item.objectStatus != ObjectStatus.global) {
                      if (widget.item.syncStatus == SyncStatus.error ||
                          widget.item.syncStatus == SyncStatus.none) {
                        FAULT_HEADER? faultHeader =
                            await DbHelper.getFaultHeaderByFaultId(
                                widget.item.fault_id.toString());
                        if (faultHeader != null) {
                          String prevStatus = "";
                          if (faultHeader.syncStatus == SyncStatus.error) {
                            prevStatus = faultHeader.status ?? "";
                            faultHeader.status = "";
                            if (prevStatus == AppConstants.completed) {
                              faultHeader.status = Constants.FAULT_STATE_OSNO;
                            }
                          }
                          await AppDatabaseManager().update(DBInputEntity(
                              FAULT_HEADER.TABLE_NAME, faultHeader.toJson()));
                          if (!kIsWeb) {
                            await PAHelper.addOrModifyFaultInAsyncMode(
                                context, faultHeader);
                          } else {
                            await PAHelper.addOrModifyFaultInSyncMode(
                                context, faultHeader);
                          }
                          if (prevStatus != "") {
                            faultHeader.status = prevStatus;
                            await AppDatabaseManager().update(DBInputEntity(
                                FAULT_HEADER.TABLE_NAME, faultHeader.toJson()));
                          }

                          final faultHeaderList =
                              ref.read(faultHeaderListProvider.notifier);
                          await faultHeaderList.fetchFaultHeaderList(plant);
                          final filteredFaultType = ref
                              .read(filteredFaultHeaderListProvider.notifier);
                          final filterOfFaultType = ref
                              .read(filterOfFaultTypeProvider.notifier)
                              .state;
                          final filterOfFaultCode = ref
                              .read(filterOfFaultCodeProvider.notifier)
                              .state;
                          final filterOfPriorityCode = ref
                              .read(filterOfFaultPriorityCodeProvider.notifier)
                              .state;
                          final filterOfPriority = ref
                              .read(filterOfFaultPriorityProvider.notifier)
                              .state;
                          final statusTypeFilter = ref
                              .read(statusTypeFaultFilterProvider.notifier)
                              .state;
                          final statusFilter = ref
                              .read(statusFaultFilterProvider.notifier)
                              .state;
                          final selectedDate =
                              ref.read(selectedCalendarDateProvider);
                          final search =
                              ref.read(searchTextProvider.notifier).state;

                          if ((search != '') ||
                              filterOfFaultType.isNotEmpty ||
                              filterOfFaultCode.isNotEmpty ||
                              filterOfPriorityCode.isNotEmpty ||
                              filterOfPriority.isNotEmpty ||
                              statusTypeFilter.isNotEmpty ||
                              statusFilter.isNotEmpty ||
                              selectedDate != null) {
                            await filteredFaultType.filteredFaultHeaderList(
                                faulttypeList: filterOfFaultCode,
                                priorityList: filterOfPriorityCode,
                                statusList: statusTypeFilter,
                                type: (search != '')
                                    ? AppConstants.search
                                    : AppConstants.faultType,
                                plantId: plant,
                                plantSec: plantSection,
                                search: search);
                          } else {
                            await filteredFaultType.filteredFaultHeaderList(
                                type: 'Initial',
                                faultList: faultHeaderList.state,
                                plantId: plant,
                                plantSec: plantSection);
                          }
                          /*               await filteredFaultType.filteredFaultHeaderList(
                              type: 'Initial',
                              faultList: faultHeaderList.state,
                              plantId: plant,
                              plantSec: plantSection);*/
                        }
                      }
                    }
                  },
                  child: Text(getSyncStatus(widget.item)),
                ))
            ],
          ),
        ),
      ),
    );
  }

  Widget _getDate(int? reqDate, int? endDate) {
    String? date;
    double clockSize = 12;
    TextStyle style = TextStyle(
      color: AppColors.secondaryTextColor,
      fontSize: 12,
      fontWeight: FontWeight.w600,
    );
    Widget widget = Container();
    if (reqDate != null && endDate == null) {
      date = UIHelper.formatDate(reqDate.toString());
      widget = Row(
        children: [
          Icon(
            Icons.access_time,
            color: Colors.blue,
            size: clockSize,
          ),
          const SizedBox(width: 4),
          Text(date, style: style),
        ],
      );
      return widget;
    } else if (reqDate == null && endDate != null) {
      date = UIHelper.formatDate(endDate.toString());
      widget = Row(
        children: [
          Icon(
            Icons.access_time,
            color: Colors.blue,
            size: clockSize,
          ),
          const SizedBox(width: 4),
          Text(
            date,
            style: style,
          ),
        ],
      );
      return widget;
    } else if (reqDate != null && endDate != null) {
      String formattedReqDate = UIHelper.formatDate(reqDate.toString());
      String formattedEndDate = UIHelper.formatDate(endDate.toString());
      date = '$formattedReqDate - $formattedEndDate';
      widget = Row(
        children: [
          Icon(
            Icons.access_time,
            color: Colors.blue,
            size: clockSize,
          ),
          const SizedBox(width: 4),
          Text(
            date,
            style: style,
          ),
        ],
      );
      return widget;
    } else {
      return widget;
    }
  }

  getFaultRightStatusActionButtons(FAULT_HEADER faultHeader) {
    if (faultHeader.status == null || faultHeader.p_mode == 'A') {
      return [
        SlidableAction(
          onPressed: (context) {},
          label: '',
        )
      ];
    } else if (faultHeader.status == Constants.FAULT_STATE_OSNO) {
      return (faultHeader.syncStatus == SyncStatus.queued ||
              faultHeader.syncStatus == SyncStatus.sent ||
              faultHeader.syncStatus == SyncStatus.error)
          ? [Container()]
          : [
              SlidableAction(
                onPressed: (context) {
                  handleStatusActionButtonsFunctions('complete', faultHeader);
                },
                backgroundColor: AppColors.greenColor,
                foregroundColor: AppColors.white,
                borderRadius: BorderRadius.circular(10),
                icon: Icons.flag,
                label: AppLocalizations.of(context)!.complete,
              )
            ];
    } else if (faultHeader.status == Constants.FAULT_STATE_ORAS &&
        (faultHeader.syncStatus == SyncStatus.none)) {
      return [
        SlidableAction(
          onPressed: (context) {},
          label: '',
        ),
      ];
    } else if (faultHeader.status == Constants.FAULT_STATE_ORAS &&
        (faultHeader.syncStatus != SyncStatus.none)) {
      return [
        SlidableAction(
          onPressed: (context) {},
          label: '',
        )
      ];
    } else if (faultHeader.status == Constants.FAULT_STATE_NOCO) {
      return [
        SlidableAction(
          onPressed: (context) {},
          label: '',
        )
      ];
    } else {
      return [
        SlidableAction(
          onPressed: (context) {},
          label: '',
        )
      ];
    }
  }

  getFaultLeftStatusActionButtons(FAULT_HEADER faultHeader) {
    if (faultHeader.status == null || faultHeader.p_mode == 'A') {
      return [
        SlidableAction(
          onPressed: (context) {},
          label: '',
        )
      ];
    } else if (faultHeader.status == Constants.FAULT_STATE_OSNO) {
      return (faultHeader.syncStatus == SyncStatus.queued ||
              faultHeader.syncStatus == SyncStatus.sent ||
              faultHeader.syncStatus == SyncStatus.error)
          ? [Container()]
          : isCreateVisible()
              ? [
                  SlidableAction(
                    onPressed: (context) {
                      handleStatusActionButtonsFunctions('assign', faultHeader);
                    },
                    backgroundColor: AppColors.blue,
                    foregroundColor: AppColors.white,
                    borderRadius: BorderRadius.circular(10),
                    icon: Icons.assignment_turned_in,
                    label: AppLocalizations.of(context)!.assign,
                  ),
                ]
              : [
                  SlidableAction(
                    onPressed: (context) {},
                    label: '',
                  )
                ];
    } else if (faultHeader.status == Constants.FAULT_STATE_ORAS &&
        (faultHeader.syncStatus != SyncStatus.none)) {
      return [
        SlidableAction(
          onPressed: (context) {},
          label: '',
        )
      ];
    } else if (faultHeader.status == Constants.FAULT_STATE_NOCO) {
      return [
        SlidableAction(
          onPressed: (context) {},
          label: '',
        )
      ];
    } else {
      return [
        SlidableAction(
          onPressed: (context) {},
          label: '',
        )
      ];
    }
  }

  handleStatusActionButtonsFunctions(
      String type, FAULT_HEADER faultHeader) async {
    if (type == 'assign') {
      bool connectionState = await Utils.hasInternetConnection();
      if (connectionState) {
        UIHelper.showConfirmationDialogWithYesOrNo(context,
            description: AppLocalizations.of(context)!
                .do_you_want_to_assign_job, yes: () {
          onYes(type, faultHeader, '', context);
        }, no: () {
          Navigator.pop(context);
        });
      } else {
        UIHelper.showErrorDialog(context,
            description: AppLocalizations.of(context)!.offline_message);
      }
    } else if (type == 'complete') {
      UIHelper.showConfirmationDialogWithYesOrNo(context,
          description: AppLocalizations.of(context)!
              .do_you_want_to_complete_fault, yes: () {
        onYes(type, faultHeader, Constants.FAULT_STATE_NOCO, context);
      }, no: () {
        Navigator.pop(context);
      });
    }
  }

  onYes(String type, FAULT_HEADER faultHeader, String statusType,
      BuildContext context) async {
    final faultHeaderData = ref.watch(faultHeaderProvider.notifier);
    final documents = ref.watch(getFaultDocumentHeaderProvider.notifier).state;
    final jobHeaderData = ref.watch(jobHeaderProvider.notifier).state;
    FAULT_HEADER header = faultHeader;
    await AppDatabaseManager()
        .update(DBInputEntity(FAULT_HEADER.TABLE_NAME, header.toJson()));
    await faultHeaderData.getFaultHeader(
        faultId: faultHeaderData.state.fault_id.toString());
    FAULT_ACTION? action =
        await DbHelper.getFaultActionByFaultId(header.fault_id.toString());
    if (action != null) {
      action.user_action = statusType;
      await AppDatabaseManager()
          .update(DBInputEntity(FAULT_ACTION.TABLE_NAME, action.toJson()));
    } else {
      FAULT_ACTION action = FAULT_ACTION(
          fault_id: header.fault_id.toString(), user_action: statusType);
      action.fid = header.lid;

      await AppDatabaseManager()
          .insert(DBInputEntity(FAULT_ACTION.TABLE_NAME, action.toJson()));
    }
    for (var data in documents) {
      FAULT_DOCUMENT newDocument = FAULT_DOCUMENT(
          fault_id: faultHeader.fault_id!.toInt(),
          doc_id: data.doc_id,
          p_mode: AppConstants.add);
      newDocument.fid = faultHeader.lid;
      FAULT_DOCUMENT? doc = await DbHelper.getFaultDocumentsByFaultId(
          faultHeader.fault_id.toString(), data.doc_id.toString());
      if (doc != null) {
      } else {
        if (doc != null) {
          DOCUMENT_HEADER? header =
              await DbHelper.getDocumentHeadersByDocsId(doc.doc_id.toString());
          if (header != null) {
            await AppDatabaseManager().insert(
                DBInputEntity(FAULT_DOCUMENT.TABLE_NAME, newDocument.toJson()));
          }
        }
      }
    }
    if (mounted) {
      Navigator.pop(context);
    }
    if (type == 'assign') {
      var result = await navigateToJobCreationScreen(faultHeader);
      if (result != null) {
        header.job_id = jobHeaderData.job_id;
        FAULT_HEADER? faultHeaderData1 =
            await DbHelper.getFaultHeaderByFaultId(header.fault_id.toString());
        if (faultHeaderData1 != null) {
          await AppDatabaseManager()
              .update(DBInputEntity(FAULT_HEADER.TABLE_NAME, header.toJson()));
        } else {
          await AppDatabaseManager()
              .insert(DBInputEntity(FAULT_HEADER.TABLE_NAME, header.toJson()));
        }
        await faultHeaderData.getFaultHeader(
            faultId: faultHeader.fault_id.toString());
        if (mounted) {
          sendToServer('assign', faultHeader, context);
        }
      }
    } else {
      if (mounted) {
        await sendToServer(type, faultHeader, context);
      }
    }
  }

  sendToServer(
      String type, FAULT_HEADER faultHeader, BuildContext context) async {
    if (type == AppConstants.review) {
      UIHelper().progressDialog(
          context: context,
          message: AppLocalizations.of(context)!.reviewing_fault);
    } else if (type == AppConstants.assign) {
      UIHelper().progressDialog(
          context: context,
          message: AppLocalizations.of(context)!.assigning_job);
    } else if (type == AppConstants.complete) {
      UIHelper().progressDialog(
          context: context,
          message: AppLocalizations.of(context)!.completing_fault);
    } else if (type == AppConstants.cancel) {
      UIHelper().progressDialog(
          context: context,
          message: AppLocalizations.of(context)!.cancelling_fault);
    }

    Result? result;
    if (!kIsWeb) {
      result = await PAHelper.addOrModifyFaultInAsyncMode(context, faultHeader);
    } else {
      result = await PAHelper.addOrModifyFaultInAsyncMode(context, faultHeader);
    }
    final plant = ref.read(plantProvider.notifier).state;
    final plantSection = ref.read(plantSectionProvider.notifier).state;
    final faultHeaderList = ref.read(faultHeaderListProvider.notifier);
    await faultHeaderList.fetchFaultHeaderList(plant);
    final filteredFaultType =
        ref.read(filteredFaultHeaderListProvider.notifier);

    final filterOfFaultType =
        ref.read(filterOfFaultTypeProvider.notifier).state;
    final filterOfFaultCode =
        ref.read(filterOfFaultCodeProvider.notifier).state;
    final filterOfPriorityCode =
        ref.read(filterOfFaultPriorityCodeProvider.notifier).state;
    final filterOfPriority =
        ref.read(filterOfFaultPriorityProvider.notifier).state;
    final statusTypeFilter =
        ref.read(statusTypeFaultFilterProvider.notifier).state;
    final statusFilter = ref.read(statusFaultFilterProvider.notifier).state;
    final selectedDate = ref.read(selectedCalendarDateProvider);
    final search = ref.read(searchTextProvider.notifier).state;

    if ((search != '') ||
        filterOfFaultType.isNotEmpty ||
        filterOfFaultCode.isNotEmpty ||
        filterOfPriorityCode.isNotEmpty ||
        filterOfPriority.isNotEmpty ||
        statusTypeFilter.isNotEmpty ||
        statusFilter.isNotEmpty ||
        selectedDate != null) {
      await filteredFaultType.filteredFaultHeaderList(
          faulttypeList: filterOfFaultCode,
          priorityList: filterOfPriorityCode,
          statusList: statusTypeFilter,
          type: (search != '') ? AppConstants.search : AppConstants.faultType,
          plantId: plant,
          plantSec: plantSection,
          search: search);
    } else {
      await filteredFaultType.filteredFaultHeaderList(
          type: 'Initial',
          faultList: faultHeaderList.state,
          plantId: plant,
          plantSec: plantSection);
    }

/*    await filteredFaultType.filteredFaultHeaderList(
        type: 'Initial',
        faultList: faultHeaderList.state,
        plantId: plant,
        plantSec: plantSection);*/

    if (result != null) {
      if (result.body['InfoMessage'] != null) {
        if (mounted) {
          Navigator.pop(context);
        }
        if (result.body['InfoMessage'][0]['category'] == 'FAILURE') {
          if (result.body['InfoMessage'][0]['message'] != null) {
            if (context.mounted) {
              UIHelper.showErrorDialog(
                context,
                description: result.body['InfoMessage'][0]['message'],
              );
            }
          }
        } else {
          if (result.body['InfoMessage'][0]['message'] != null) {
            if (context.mounted) {
              UIHelper.showErrorDialog(context,
                  description: result.body['InfoMessage'][0]['message']);
            }
          }
        }
      } else {
        if (mounted) {
          Navigator.pop(context);
          // //TODO
          Navigator.pop(context);
        }
      }
    }
  }

  navigateToJobCreationScreen(FAULT_HEADER faultHeader) async {
    clearJobStates();
    final editJobCreation = ref.watch(editJobCreationProvider.notifier);
    final plant = ref.watch(plantProvider.notifier).state;
    final jobHeaderInsertion = ref.watch(insertJobHeaderProvider.notifier);
    final jobHeader = ref.watch(jobHeaderProvider.notifier);
    final jobAction = ref.watch(getJobActionProvider.notifier);
    final jobDocument = ref.watch(getJobDocumentProvider.notifier);
    editJobCreation.getEditJobCreationEnable(true);
    DateTime now = DateTime.now();
    String faultNoticedOnDate = DateFormat('dd MMM yyyy').format(now);
    DateTime dateTime = DateFormat("dd MMM yyyy").parse(faultNoticedOnDate);
    DateTime adjustedDate = DateTime(
      dateTime.year,
      dateTime.month,
      dateTime.day,
    );
    String formattedDate = DateFormat("yyyyMMdd").format(adjustedDate);
    int dateAsInt = int.parse(formattedDate);
    JOB_HEADER newJobHeader = JOB_HEADER(
        plant_id: plant,
        job_id: UIHelper.generateRandomId(),
        p_mode: AppConstants.add,
        fault_id: faultHeader.fault_id,
        location_id: faultHeader.location_id,
        asset_no: faultHeader.asset_no,
        priority: faultHeader.priority,
        start_date: dateAsInt);
    await jobHeaderInsertion.insertJobHeader(newJobHeader);
    await jobHeader.getJobHeader(jobId: newJobHeader.job_id.toString());
    await jobAction.getJobAction(newJobHeader.job_id.toString());
    await jobDocument.getJobDocuments(newJobHeader.job_id.toString());
    docAttachments.clear();
    if (mounted) {
      var result =
          await Navigator.push(context, MaterialPageRoute(builder: (context) {
        return JobCreationDetailScreen(
          fromFault: false,
        );
      }));
      if (result != null) {
        final faultHeaderData = ref.watch(faultHeaderProvider.notifier);
        await faultHeaderData.getFaultHeader(
            faultId: faultHeader.fault_id.toString());
        setState(() {});
      }
    }
  }

  clearJobStates() {
    final description = ref.watch(jobDescriptionProvider.notifier);
    final longText = ref.watch(jobLongTextProvider.notifier);
    final priority = ref.watch(jobPriorityProvider.notifier);
    final jobType = ref.watch(jobTypeProvider.notifier);
    final assignedTo = ref.watch(jobAssignedToProvider.notifier);
    final startDate = ref.watch(jobStartOnProvider.notifier);
    final endDate = ref.watch(jobEndOnProvider.notifier);
    description.clearJobDescription();
    longText.clearLongText();
    priority.clearPriority();
    jobType.clearJobType();
    assignedTo.clearAssignedTo();
    startDate.clearStartDate();
    endDate.clearEndDate();
  }

  ///Redo
  String getSyncStatus(FAULT_HEADER item) {
    switch (item.syncStatus.index) {
      case 0:
        if (item.objectStatus.index != 0) {
          return '';
        }
        if (item.objectStatus.index == 2) {
          return AppLocalizations.of(context)!.sync_now;
        }
        return '';

      case 1:
        return AppLocalizations.of(context)!.queued;

      case 2:
        return AppLocalizations.of(context)!.sent;

      case 3:
        return AppLocalizations.of(context)!.error;

      default:
        return ''; // Default return value if no cases match
    }
  }

  Color getSyncStatusColor(FAULT_HEADER item) {
    switch (item.syncStatus.index ?? 0) {
      case 0:
        return Colors.transparent; // Default transparent color for case 0
      case 1:
        return Colors.orange; // Orange for 'Queued'
      case 2:
        return AppColors.primaryColor; // Green for 'Sent'
      case 3:
        return Colors.red; // Red for 'Error'
      default:
        return Colors.transparent; // Default color if no cases match
    }
  }

  bool isAssignVisible() {
    final role = ref.watch(roleProvider);
    if (role != null) {
      if (UIHelper.isAssign(role.fault!)) {
        return true;
      } else {
        return false;
      }
    } else {
      return false;
    }
  }

  bool isDisplayVisible() {
    final role = ref.watch(roleProvider);
    if (role != null) {
      if (UIHelper.isDisplay(role.fault!)) {
        return true;
      } else {
        return false;
      }
    } else {
      return false;
    }
  }

  bool isExecutionVisible() {
    final role = ref.watch(roleProvider);
    if (role != null) {
      if (UIHelper.isExecute(role.fault!)) {
        return true;
      } else {
        return false;
      }
    } else {
      return false;
    }
  }

  bool isCreateVisible() {
    final role = ref.watch(roleProvider);
    if (role != null) {
      if (UIHelper.isCreate(role.task!)) {
        return true;
      } else {
        return false;
      }
    } else {
      return false;
    }
  }

  void onUnsaveFault(FAULT_HEADER faultHeader) async {
    final editProviderr = ref.watch(editFaultFieldProvider.notifier);
    Navigator.pop(context);
    if (!kIsWeb) {
      await AppDatabaseManager()
          .delete(DBInputEntity(FAULT_HEADER.TABLE_NAME, faultHeader.toJson()));
      if (ScreenType.desktop != UIHelper().getScreenType(context)) {
        Navigator.pop(context);
      }
    } else {
      editProviderr.getEditFaultFieldEnable(false);
      widget.onTap(InteractiveItemModel(
          type: "FAULT_HEADER",
          data: {"type": AppConstants.fault, "index": widget.index}));
    }
    final plant = ref.read(plantProvider.notifier).state;
    final plantSection = ref.read(plantSectionProvider.notifier).state;
    final faultHeaderList = ref.read(faultHeaderListProvider.notifier);
    await faultHeaderList.fetchFaultHeaderList(plant);
    final filteredFaultType =
        ref.read(filteredFaultHeaderListProvider.notifier);

    // final faultHeaderDatas = ref.read(faultHeaderListProvider);
    final filterOfFaultType =
        ref.read(filterOfFaultTypeProvider.notifier).state;
    final filterOfFaultCode =
        ref.read(filterOfFaultCodeProvider.notifier).state;
    final filterOfPriorityCode =
        ref.read(filterOfFaultPriorityCodeProvider.notifier).state;
    final filterOfPriority =
        ref.read(filterOfFaultPriorityProvider.notifier).state;
    final statusTypeFilter =
        ref.read(statusTypeFaultFilterProvider.notifier).state;
    final statusFilter = ref.read(statusFaultFilterProvider.notifier).state;
    final selectedDate = ref.read(selectedCalendarDateProvider);
    final search = ref.read(searchTextProvider.notifier).state;

    if ((search != '') ||
        filterOfFaultType.isNotEmpty ||
        filterOfFaultCode.isNotEmpty ||
        filterOfPriorityCode.isNotEmpty ||
        filterOfPriority.isNotEmpty ||
        statusTypeFilter.isNotEmpty ||
        statusFilter.isNotEmpty ||
        selectedDate != null) {
      await filteredFaultType.filteredFaultHeaderList(
          faulttypeList: filterOfFaultCode,
          priorityList: filterOfPriorityCode,
          statusList: statusTypeFilter,
          type: (search != '') ? AppConstants.search : AppConstants.faultType,
          plantId: plant,
          plantSec: plantSection,
          search: search);
    } else {
      await filteredFaultType.filteredFaultHeaderList(
          type: 'Initial',
          faultList: faultHeaderList.state,
          // faultList: faultHeaderDatas,
          plantId: plant,
          plantSec: plantSection);
    }

    if (ScreenType.desktop == UIHelper().getScreenType(context)) {
      clearFaultStates();
    }

/*    await filteredFaultType.filteredFaultHeaderList(
        type: 'Initial',
        faultList: faultHeaderList.state,
        plantId: plant,
        plantSec: plantSection);*/
  }

  void onCheck(String? status, {bool willScope = false}) async {
    if (status != null) {
      if (willScope) {
        await saveGeneralModifiedFault();
        await saveModifiedFault();
        ref.read(faultDescriptionProvider.notifier).resetFaultDescription();
        ref.read(faultLongTextProvider.notifier).resetFaultLongText();
      } else {
        UIHelper.showConfirmationDialogWithYesOrNo(context,
            description: AppLocalizations.of(context)!
                .confirmation_saving_modified_fault, yes: () async {
          Navigator.pop(context);
          await saveGeneralModifiedFault();
          await saveModifiedFault();
          ref.read(faultDescriptionProvider.notifier).resetFaultDescription();
          ref.read(faultLongTextProvider.notifier).resetFaultLongText();
        }, no: () {
          Navigator.pop(context);
        });
      }
    } else {
      if (!validate()) {
        return;
      }
      if (willScope) {
        onNewFaultSave();
      } else {
        UIHelper.showConfirmationDialog(
          context,
          description: AppLocalizations.of(context)!.save_fault_confirmation,
          positiveButtonString: AppLocalizations.of(context)!.ok,
          positiveButtonOnTap: () {
            Navigator.pop(context);
            onNewFaultSave();
          },
        );
      }
    }
  }

  bool validate() {
    final attachment = ref.watch(getFaultDocumentProvider.notifier).state;
    final faultNoticedOnController =
        ref.watch(faultNoticedOnProvider.notifier).state;
    final dueOnController = ref.watch(faultDueOnProvider.notifier).state;
    final selectedLocation = ref.watch(locationProvider.notifier).state;
    final assetLocList = ref.read(assetLocListProvider.notifier).state;
    final selectedAsset = ref.watch(assetProvider.notifier).state;
    final selectedPlant = ref.watch(plantProvider.notifier).state;
    final selectedDescription =
        ref.watch(faultDescriptionProvider.notifier).state;
    final selectedLongText = ref.watch(faultLongTextProvider.notifier).state;
    final selectedFailureMode =
        ref.watch(faultModeHeaderProvider.notifier).state;
    final selectedFaultType = ref.watch(faultTypeProvider.notifier).state;
    final selectedPriority = ref.watch(priorityProvider.notifier).state;

    bool data = false;

    ///TODO CILT
/*    if (widget.type != AppConstants.ciltFault) {
      if (selectedLocation.isEmpty) {
        UIHelper.showErrorDialog(context,
            description: AppLocalizations.of(context)!.please_enter_location);
        return data;
      }
    }*/
    if (assetLocList.isNotEmpty) {
      if (selectedAsset.isEmpty) {
        UIHelper.showErrorDialog(context,
            description: AppLocalizations.of(context)!.please_enter_asset);
        return data;
      }
    }
    if (selectedPlant.isEmpty) {
      UIHelper.showErrorDialog(context,
          description: AppLocalizations.of(context)!.please_enter_plant);
      return data;
    }

    if (selectedDescription.isEmpty) {
      UIHelper.showErrorDialog(context,
          description: AppLocalizations.of(context)!.please_enter_description);
      return data;
    }
    if (selectedLongText.isEmpty) {
      UIHelper.showErrorDialog(context,
          description: AppLocalizations.of(context)!.please_enter_long_text);
      return data;
    } else if (selectedFaultType.description == null) {
      UIHelper.showErrorDialog(context,
          description: AppLocalizations.of(context)!.please_select_faultType);
      return data;
    } else if (selectedPriority.description == null) {
      UIHelper.showErrorDialog(context,
          description: AppLocalizations.of(context)!.please_select_priority);
      return data;
    } else if (selectedFailureMode.description == null) {
      UIHelper.showErrorDialog(context,
          description: AppLocalizations.of(context)!.please_select_failureMode);
      return data;
    } else if (faultNoticedOnController == 0) {
      UIHelper.showErrorDialog(context,
          description:
              AppLocalizations.of(context)!.please_select_faultNoticedDate);
      return data;
    } else if (dueOnController == 0) {
      UIHelper.showErrorDialog(context,
          description: AppLocalizations.of(context)!.please_select_dueDate);
      return data;
    } else if (attachment.isEmpty) {
      UIHelper.showErrorDialog(context,
          description: AppLocalizations.of(context)!
              .please_upload_atleast_one_attachment);
      return data;
    }
    return true;
  }

  void onNewFaultSave() async {
    try {
      if (!(await URLService.isInternetConnected())) {
        if (mounted) {
          UIHelper.showErrorDialog(
            context,
            description:
                AppLocalizations.of(context)!.noInternetConnectivityString,
          );
        }
      } else {
        if (mounted) {
          UIHelper().progressDialog(
              context: context,
              message: AppLocalizations.of(context)!.saving_fault);
          final header =
              ref.watch(getFaultDocumentHeaderProvider.notifier).state;
          if(kIsWeb){
            Result? result =
                await PAHelper.addDocumentInSyncMode(context, header);
          }
          else{
            Result? result =
            await PAHelper.addDocumentInAsyncMode(context, header);
          }
          await SyncEngine().receive();
          await creationOfFault();
        }
      }
    } catch (e) {
      Logger.logError(sourceClass, 'onNewFaultSave', e.toString());
    }
  }

  Future<void> creationOfFault() async {
    try {
      final faultMode = ref.watch(faultModeHeaderProvider.notifier).state;
      final priority = ref.watch(priorityProvider.notifier).state;
      final faultType = ref.watch(faultTypeProvider.notifier).state;
      final header = ref.watch(getFaultDocumentHeaderProvider.notifier).state;
      var ciltTask;
      var ciltHeader;
      var ciltExecTask;

      ///TODO CILT
/*      if (widget.type == AppConstants.ciltFault) {
        ciltTask = ref.watch(ciltTaskProvider.notifier).state;
        ciltHeader = ref.watch(ciltHeaderProvider);
        ciltExecTask = ref
            .watch(ciltExecuteTaskListProvider.notifier)
            .getciltExecTaskHeaderByTask(ciltTask, ciltHeader);
      }*/

      final faultHeader = ref.watch(faultHeaderProvider.notifier).state;
      final faultDescription =
          ref.watch(faultDescriptionProvider.notifier).state;
      final longText = ref.watch(faultLongTextProvider.notifier).state;
      final location = ref.read(locationProvider.notifier).state;
      final asset = ref.read(assetProvider.notifier).state;
      final plant = ref.read(plantProvider.notifier).state;
      final user = ref.watch(userProvider);
      var faultReportedByController =
          ref.watch(faultReportedByProvider.notifier).state;
      faultReportedByController = user!.user_id.toString();
      String reportedBy = faultReportedByController;
      final faultNoticedOnController =
          ref.watch(faultNoticedOnProvider.notifier).state;
      final dueOnController = ref.watch(faultDueOnProvider.notifier).state;
      faultHeader.location_id = location;
      if (asset.isNotEmpty) {
        faultHeader.asset_no = int.parse(asset);
      }
      faultHeader.plant_id = plant;
      faultHeader.description = faultDescription;
      faultHeader.details = longText;
      faultHeader.priority = priority.priority_code;
      faultHeader.failure_mode = faultMode.failure_code;
      faultHeader.fault_type = faultType.fault_code;

      ///TODO CILT
/*      faultHeader.cilt_task_id = widget.type == AppConstants.ciltFault
          ? ciltExecTask.cilt_task_id
          : null;*/

      faultHeader.reported_by = reportedBy;
      faultHeader.reported_on = faultNoticedOnController;
      if (dueOnController != 0) {
        faultHeader.req_end = dueOnController;
      }
      await AppDatabaseManager()
          .update(DBInputEntity(FAULT_HEADER.TABLE_NAME, faultHeader.toJson()));

      FAULT_ACTION newAction = FAULT_ACTION(
          fault_id: faultHeader.fault_id!.toString(),
          user_action: Constants.FAULT_STATE_OSNO);
      newAction.fid = faultHeader.lid;
      FAULT_ACTION? action = await DbHelper.getFaultActionByFaultId(
          faultHeader.fault_id.toString());
      if (action != null) {
        await AppDatabaseManager()
            .update(DBInputEntity(FAULT_ACTION.TABLE_NAME, action.toJson()));
      } else {
        await AppDatabaseManager()
            .insert(DBInputEntity(FAULT_ACTION.TABLE_NAME, newAction.toJson()));
      }
      for (var data in header) {
        FAULT_DOCUMENT newDocument = FAULT_DOCUMENT(
            fault_id: faultHeader.fault_id!.toInt(),
            doc_id: data.doc_id,
            p_mode: AppConstants.add);
        newDocument.fid = faultHeader.lid;
        FAULT_DOCUMENT? doc = await DbHelper.getFaultDocumentsByFaultId(
            faultHeader.fault_id.toString(), data.doc_id.toString());
        if (doc != null) {
          await AppDatabaseManager()
              .update(DBInputEntity(FAULT_DOCUMENT.TABLE_NAME, doc.toJson()));
        } else {
          await AppDatabaseManager().insert(
              DBInputEntity(FAULT_DOCUMENT.TABLE_NAME, newDocument.toJson()));
        }
      }

      if (!kIsWeb) {
        await PAHelper.addOrModifyFaultInAsyncMode(context, faultHeader);
      } else {
        await PAHelper.addOrModifyFaultInSyncMode(context, faultHeader);
      }

      final plantSection = ref.read(plantSectionProvider.notifier).state;
      final faultHeaderList = ref.read(faultHeaderListProvider.notifier);
      await faultHeaderList.fetchFaultHeaderList(plant);
      final filteredFaultType =
          ref.read(filteredFaultHeaderListProvider.notifier);

      // final faultHeaderDatas = ref.read(faultHeaderListProvider);
      final filterOfFaultType =
          ref.read(filterOfFaultTypeProvider.notifier).state;
      final filterOfFaultCode =
          ref.read(filterOfFaultCodeProvider.notifier).state;
      final filterOfPriorityCode =
          ref.read(filterOfFaultPriorityCodeProvider.notifier).state;
      final filterOfPriority =
          ref.read(filterOfFaultPriorityProvider.notifier).state;
      final statusTypeFilter =
          ref.read(statusTypeFaultFilterProvider.notifier).state;
      final statusFilter = ref.read(statusFaultFilterProvider.notifier).state;
      final selectedDate = ref.read(selectedCalendarDateProvider);
      final search = ref.read(searchTextProvider.notifier).state;

      if ((search != '') ||
          filterOfFaultType.isNotEmpty ||
          filterOfFaultCode.isNotEmpty ||
          filterOfPriorityCode.isNotEmpty ||
          filterOfPriority.isNotEmpty ||
          statusTypeFilter.isNotEmpty ||
          statusFilter.isNotEmpty ||
          selectedDate != null) {
        await filteredFaultType.filteredFaultHeaderList(
            faulttypeList: filterOfFaultCode,
            priorityList: filterOfPriorityCode,
            statusList: statusTypeFilter,
            type: (search != '') ? AppConstants.search : AppConstants.faultType,
            plantId: plant,
            plantSec: plantSection,
            search: search);
      } else {
        await filteredFaultType.filteredFaultHeaderList(
            type: 'Initial',
            // faultList: faultHeaderDatas,
            faultList: faultHeaderList.state,
            plantId: plant,
            plantSec: plantSection);
      }

/*      await filteredFaultType.filteredFaultHeaderList(
          type: 'Initial',
          faultList: faultHeaderList.state,
          plantId: plantt,
          plantSec: planttSection);*/
      Navigator.pop(context);
      Navigator.pop(context, true);
    } catch (e) {
      Logger.logError(sourceClass, 'creationOfFault', e.toString());
    }
  }

  Future<void> saveGeneralModifiedFault() async {
    try {
      final faultHeaderr = ref.read(faultHeaderProvider.notifier).state;
      final user = ref.watch(userProvider);

      UIHelper().progressDialog(
          context: context,
          message: AppLocalizations.of(context)!.saving_fault_changes);

      final editProvider = ref.watch(editFaultFieldProvider.notifier);
      final faultHeaderProviderr = ref.watch(faultHeaderProvider.notifier);
      final faultDescription =
          ref.watch(faultDescriptionProvider.notifier).state;

      final faultNoticedOn = ref.watch(faultNoticedOnProvider.notifier).state;
      final faultDueOn = ref.watch(faultDueOnProvider.notifier).state;
      final faultMode = ref.watch(faultModeHeaderProvider.notifier).state;
      final priority = ref.watch(priorityProvider.notifier).state;
      final faultType = ref.watch(faultTypeProvider.notifier).state;
      final longText = ref.read(faultLongTextProvider.notifier).state;

      FAULT_HEADER faultHeader = faultHeaderr;
      if (faultNoticedOn != 0) {
        faultHeader.reported_on = faultNoticedOn;
      }
      if (faultDueOn != 0) {
        faultHeader.req_end = faultDueOn;
      }
      /*     if (faultReportedBy.isNotEmpty || faultReportedBy != '') {
        faultHeader.reported_by = reportedBy;
      }*/
      if (faultDescription.isNotEmpty || faultDescription != '') {
        faultHeader.description = faultDescription;
      }

      if (longText.isNotEmpty || longText != '') {
        faultHeader.details = longText;
      }

      if (faultMode.failure_code != null || faultMode.failure_code != '') {
        faultHeader.failure_mode = faultMode.failure_code;
      }
      if (priority.priority_code != null || priority.priority_code != '') {
        faultHeader.priority = priority.priority_code;
      }
      if (faultType.fault_code != null || faultType.fault_code != '') {
        faultHeader.fault_type = faultType.fault_code;
      }
      if (faultHeader.p_mode != AppConstants.add) {
        faultHeader.p_mode = AppConstants.modified;
      }
      await AppDatabaseManager()
          .update(DBInputEntity(FAULT_HEADER.TABLE_NAME, faultHeader.toJson()));

      editProvider.getEditFaultFieldEnable(!editProvider.state);
      await faultHeaderProviderr.getFaultHeader(
          faultId: faultHeader.fault_id.toString());
      setState(() {});
    } catch (e) {
      Logger.logError(sourceClass, 'saveGeneralModifiedFault', e.toString());
    }
  }

  Future<void> saveModifiedFault() async {
    try {
      final faultHeader = ref.read(faultHeaderProvider.notifier).state;
      final headerData =
          ref.watch(getFaultDocumentHeaderProvider.notifier).state;
      FAULT_ACTION? actionData = await DbHelper.getFaultActionByFaultId(
          faultHeader.fault_id.toString());
      if (actionData == null) {
        FAULT_ACTION action = FAULT_ACTION(
            fault_id: faultHeader.fault_id!.toString(),
            user_action: Constants.FAULT_STATE_OSNO);
        action.fid = faultHeader.lid;
        await AppDatabaseManager()
            .insert(DBInputEntity(FAULT_ACTION.TABLE_NAME, action.toJson()));
      } else {
        await AppDatabaseManager().update(
            DBInputEntity(FAULT_ACTION.TABLE_NAME, actionData.toJson()));
      }

      List<DOCUMENT_HEADER> documents = headerData
          .where((element) => element.objectStatus == ObjectStatus.add)
          .toList();
      if (kIsWeb) {
        await PAHelper.addDocumentInSyncMode(context, documents);
      } else {
        await PAHelper.addDocumentInAsyncMode(context, documents);
      }
      Result? result;
      if (!kIsWeb) {
        result =
            await PAHelper.addOrModifyFaultInAsyncMode(context, faultHeader);
      } else {
        result =
            await PAHelper.addOrModifyFaultInSyncMode(context, faultHeader);
        Navigator.pop(context);
        if (result.body != null) {
          //filter out the displayed list and find the index to update the sected fault

          final faultHeaderList = ref.watch(faultHeaderListProvider);
          final filteredFaultHeader =
              ref.watch(filteredFaultHeaderListProvider);

          final isFiltering = filteredFaultHeader != faultHeaderList;
          final displayedList =
              isFiltering ? filteredFaultHeader : faultHeaderList;

          displayedList.sort((a, b) {
            DateTime dateA = convertToDateTime(a.reported_on.toString());
            DateTime dateB = convertToDateTime(b.reported_on.toString());
            return dateA.compareTo(dateB);
          });

          //find index of the fault from displayedList

          int index = displayedList.indexWhere(
              (element) => element.fault_id == faultHeader.fault_id);

          widget.onTap(InteractiveItemModel(
              type: "FAULT_HEADER",
              data: {"type": AppConstants.fault, "index": index}));
        }
      }
      if (!kIsWeb) {
        if (mounted) {
          Navigator.pop(context);
        }
      }
      final plant = ref.read(plantProvider.notifier).state;
      final plantSection = ref.read(plantSectionProvider.notifier).state;
      final faultHeaderList = ref.read(faultHeaderListProvider.notifier);
      await faultHeaderList.fetchFaultHeaderList(plant);
      final filteredFaultType =
          ref.read(filteredFaultHeaderListProvider.notifier);
/*      await filteredFaultType.filteredFaultHeaderList(
          type: 'Initial',
          faultList: faultHeaderList.state,
          plantId: plant,
          plantSec: plantSection);*/
      // final faultHeaderDatas = ref.read(faultHeaderListProvider);
      final filterOfFaultType =
          ref.read(filterOfFaultTypeProvider.notifier).state;
      final filterOfFaultCode =
          ref.read(filterOfFaultCodeProvider.notifier).state;
      final filterOfPriorityCode =
          ref.read(filterOfFaultPriorityCodeProvider.notifier).state;
      final filterOfPriority =
          ref.read(filterOfFaultPriorityProvider.notifier).state;
      final statusTypeFilter =
          ref.read(statusTypeFaultFilterProvider.notifier).state;
      final statusFilter = ref.read(statusFaultFilterProvider.notifier).state;
      final selectedDate = ref.read(selectedCalendarDateProvider);
      final search = ref.read(searchTextProvider.notifier).state;

      if ((search != '') ||
          filterOfFaultType.isNotEmpty ||
          filterOfFaultCode.isNotEmpty ||
          filterOfPriorityCode.isNotEmpty ||
          filterOfPriority.isNotEmpty ||
          statusTypeFilter.isNotEmpty ||
          statusFilter.isNotEmpty ||
          selectedDate != null) {
        await filteredFaultType.filteredFaultHeaderList(
            faulttypeList: filterOfFaultCode,
            priorityList: filterOfPriorityCode,
            statusList: statusTypeFilter,
            type: (search != '') ? AppConstants.search : AppConstants.faultType,
            plantId: plant,
            plantSec: plantSection,
            search: search);
      } else {
        await filteredFaultType.filteredFaultHeaderList(
            type: 'Initial',
            faultList: faultHeaderList.state,
            // faultList: faultHeaderDatas,
            plantId: plant,
            plantSec: plantSection);
      }
    } catch (e) {
      Logger.logError(sourceClass, 'saveModifiedFault', e.toString());
    }
  }

  Future<bool> checkIfModifiedDataThere() async {
    final faultHeaderr = ref.watch(faultHeaderProvider.notifier).state;
    final originalFaultHeader = ref.watch(faultHeaderProvider);
    final faultDescription = ref.watch(faultDescriptionProvider.notifier).state;
    final faultNoticedOn = ref.watch(faultNoticedOnProvider.notifier).state;
    final faultDueOn = ref.watch(faultDueOnProvider.notifier).state;
    final faultMode = ref.watch(faultModeHeaderProvider.notifier).state;
    final priority = ref.watch(priorityProvider.notifier).state;
    final faultType = ref.watch(faultTypeProvider.notifier).state;
    final longText = ref.read(faultLongTextProvider.notifier).state;
    FAULT_HEADER faultHeader = FAULT_HEADER.fromJson(faultHeaderr.toJson());
   
    if (faultNoticedOn != 0) {
      faultHeader.reported_on = faultNoticedOn;
    }
    if (faultDueOn != 0) {
      faultHeader.req_end = faultDueOn;
    }
    if (faultDescription.isNotEmpty && faultDescription != '') {
      faultHeader.description = faultDescription;
    }
    if (longText.isNotEmpty && longText != '') {
      faultHeader.details = longText;
    }
    if (faultMode.failure_code != null && faultMode.failure_code != '') {
      faultHeader.failure_mode = faultMode.failure_code;
    }
    if (priority.priority_code != null && priority.priority_code != '') {
      faultHeader.priority = priority.priority_code;
    }
    if (faultType.fault_code != null && faultType.fault_code != '') {
      faultHeader.fault_type = faultType.fault_code;
    }
    bool edited = true;
    edited = areJsonEqual(originalFaultHeader.toJson(), faultHeader.toJson());
    return edited;
  }

  bool areJsonEqual(Map<String, dynamic> json1, Map<String, dynamic> json2) {
    return const DeepCollectionEquality().equals(json1, json2);
  }

  // Modern badge builders
  Widget _buildPriorityBadge() {
    String priorityText = ref
        .watch(priorityListProvider.notifier)
        .fetchPriorityCode(widget.item.priority.toString());

    Color badgeColor;
    switch (priorityText.toLowerCase()) {
      case 'high':
        badgeColor = AppColors.highPriorityBadge;
        break;
      case 'medium':
        badgeColor = AppColors.mediumPriorityBadge;
        break;
      case 'low':
        badgeColor = AppColors.lowPriorityBadge;
        break;
      default:
        badgeColor = AppColors.modernTertiaryText;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: badgeColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: badgeColor.withOpacity(0.3)),
      ),
      child: Text(
        priorityText,
        style: UIHelper.modernCaptionStyle(
          fontSize: 12,
          fontWeight: FontWeight.w600, // font-semibold equivalent
          color: badgeColor,
        ),
      ),
    );
  }

  Widget _buildStatusBadge() {
    String statusText = UIHelper.getStatusString(widget.item.status.toString());
    Color badgeColor;

    switch (statusText.toLowerCase()) {
      case 'open':
        badgeColor = AppColors.openStatusColor;
        break;
      case 'assigned':
      case 'job assigned':
        badgeColor = AppColors.assignedStatusColor;
        break;
      case 'completed':
        badgeColor = AppColors.completedStatusColor;
        break;
      default:
        badgeColor = AppColors.modernTertiaryText;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: badgeColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: badgeColor.withOpacity(0.3)),
      ),
      child: Text(
        statusText,
        style: UIHelper.modernCaptionStyle(
          fontSize: 12,
          fontWeight: FontWeight.w600, // font-semibold equivalent
          color: badgeColor,
        ),
      ),
    );
  }

  Widget _buildFaultTypeBadge() {
    String faultTypeText = ref
        .watch(faultTypeListProvider.notifier)
        .fetchFaultTypeCode(widget.item.fault_type.toString());

    Color badgeColor;
    switch (faultTypeText.toLowerCase()) {
      case 'mechanical':
      case 'mechanical breakdown':
        badgeColor = const Color(0xFFDC2626); // Red
        break;
      case 'electrical':
        badgeColor = const Color(0xFFEA580C); // Orange
        break;
      case 'hydraulic':
        badgeColor = const Color(0xFF2563EB); // Blue
        break;
      case 'instrument':
      case 'instrumentation':
        badgeColor = const Color(0xFF7C3AED); // Purple
        break;
      case 'safety':
        badgeColor = const Color(0xFFDC2626); // Red
        break;
      case 'process':
        badgeColor = const Color(0xFF059669); // Green
        break;
      case 'quality':
        badgeColor = const Color(0xFFD97706); // Amber
        break;
      default:
        badgeColor = const Color(0xFF6B7280); // Grey
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: badgeColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: badgeColor.withOpacity(0.3)),
      ),
      child: Text(
        faultTypeText,
        style: UIHelper.modernCaptionStyle(
          fontSize: 12,
          fontWeight: FontWeight.w600, // font-semibold equivalent
          color: badgeColor,
        ),
      ),
    );
  }

  String _getFormattedDate(int? dateInt) {
    if (dateInt == null) return '';
    try {
      // The dateInt is in YYYYMMDD format, not milliseconds
      return UIHelper.formatDate(dateInt.toString());
    } catch (e) {
      return '';
    }
  }

  String _getAssetDescription() {
    // Display asset name if available, otherwise display location name
    if (widget.item.asset_no != null) {
      // Try to get asset description from asset providers
      final assetList = ref.watch(assetHeaderProvider);
      try {
        final asset = assetList.firstWhere(
          (element) => element.asset_no == widget.item.asset_no,
        );
        if (asset.description != null && asset.description!.isNotEmpty) {
          return asset.description!;
        }
        return 'Asset ${widget.item.asset_no}';
      } catch (e) {
        return 'Asset ${widget.item.asset_no}';
      }
    } else if (widget.item.location_id != null) {
      // Try to get location description from location providers
      final flocList = ref.watch(flocHeaderProvider);
      try {
        final floc = flocList.firstWhere(
          (element) => element.location_id == widget.item.location_id,
        );
        if (floc.description != null && floc.description!.isNotEmpty) {
          return floc.description!;
        }
        return widget.item.location_id.toString();
      } catch (e) {
        return widget.item.location_id.toString();
      }
    }
    return 'Asset/Equipment';
  }

  IconData _getAssetLocationIcon() {
    // Return equipment icon if displaying asset name, location icon if displaying location name
    if (widget.item.asset_no != null) {
      // Try to get asset description from asset providers
      final assetList = ref.watch(assetHeaderProvider);
      try {
        final asset = assetList.firstWhere(
          (element) => element.asset_no == widget.item.asset_no,
        );
        if (asset.description != null && asset.description!.isNotEmpty) {
          return Icons.precision_manufacturing_outlined; // Equipment/Asset icon
        }
        return Icons.precision_manufacturing_outlined; // Equipment/Asset icon
      } catch (e) {
        return Icons.precision_manufacturing_outlined; // Equipment/Asset icon
      }
    } else if (widget.item.location_id != null) {
      // Try to get location description from location providers
      final flocList = ref.watch(flocHeaderProvider);
      try {
        final floc = flocList.firstWhere(
          (element) => element.location_id == widget.item.location_id,
        );
        if (floc.description != null && floc.description!.isNotEmpty) {
          return Icons.location_on_outlined; // Location icon
        }
        return Icons.location_on_outlined; // Location icon
      } catch (e) {
        return Icons.location_on_outlined; // Location icon
      }
    }
    return Icons.precision_manufacturing_outlined; // Default to equipment icon
  }

  Widget _buildAssignmentStatusBadge() {
    // Check if fault has a job assigned
    if (widget.item.job_id != null) {
      return Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            // "Assigned" part with lighter greenish color
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: const Color(0xFF10B981).withOpacity(0.1),
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(12),
                  bottomLeft: Radius.circular(12),
                ),
                border: Border.all(color: const Color(0xFF10B981).withOpacity(0.3)),
              ),
              child: Text(
                'Assigned',
                style: UIHelper.modernCaptionStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w600, // font-semibold equivalent
                  color: const Color(0xFF10B981),
                ),
              ),
            ),
            // Job number part with lighter blue-grey color
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: const Color(0xFF6B7280).withOpacity(0.1),
                borderRadius: const BorderRadius.only(
                  topRight: Radius.circular(12),
                  bottomRight: Radius.circular(12),
                ),
                border: Border.all(color: const Color(0xFF6B7280).withOpacity(0.3)),
              ),
              child: Text(
                widget.item.job_id.toString(),
                style: UIHelper.modernCaptionStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w600, // font-semibold equivalent
                  color: const Color(0xFF6B7280),
                ),
              ),
            ),
          ],
        ),
      );
    } else {
      // Show status badge if no job assigned
      return _buildStatusBadge();
    }
  }
}
